#!/usr/bin/env dart

import 'dart:io';

/// Test runner script for the Graduation Algorithm System
/// 
/// This script provides an easy way to run different test suites
/// Usage: dart run_tests.dart [option]
/// 
/// Options:
///   all       - Run all tests (default)
///   helper    - Run helper function tests only
///   io        - Run IO operation tests only
///   filter    - Run FilterSubjects class tests only
///   original  - Run original core tests only
///   main      - Run main application demo

void main(List<String> arguments) async {
  print('🎓 Graduation Algorithm System - Test Runner');
  print('=' * 50);
  
  final option = arguments.isNotEmpty ? arguments[0].toLowerCase() : 'all';
  
  switch (option) {
    case 'all':
      await runAllTests();
      break;
    case 'helper':
      await runHelperTests();
      break;
    case 'io':
      await runIOTests();
      break;
    case 'filter':
      await runFilterTests();
      break;
    case 'original':
      await runOriginalTests();
      break;
    case 'main':
      await runMainDemo();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      print('❌ Unknown option: $option');
      showHelp();
      exit(1);
  }
}

Future<void> runAllTests() async {
  print('🚀 Running all tests...\n');
  await runCommand('dart', ['test', 'test/test_all.dart']);
}

Future<void> runHelperTests() async {
  print('🛠️ Running helper function tests...\n');
  await runCommand('dart', ['test', 'test/test_helper.dart']);
}

Future<void> runIOTests() async {
  print('💾 Running IO operation tests...\n');
  await runCommand('dart', ['test', 'test/test_io.dart']);
}

Future<void> runFilterTests() async {
  print('🎯 Running FilterSubjects class tests...\n');
  await runCommand('dart', ['test', 'test/test_filter_subjects.dart']);
}

Future<void> runOriginalTests() async {
  print('📚 Running original core tests...\n');
  await runCommand('dart', ['test', 'test/test.dart']);
}

Future<void> runMainDemo() async {
  print('🎮 Running main application demo...\n');
  await runCommand('dart', ['run', 'main.dart']);
}

Future<void> runCommand(String command, List<String> arguments) async {
  final process = await Process.start(command, arguments);
  
  // Forward stdout and stderr
  process.stdout.listen((data) {
    stdout.add(data);
  });
  
  process.stderr.listen((data) {
    stderr.add(data);
  });
  
  final exitCode = await process.exitCode;
  
  if (exitCode == 0) {
    print('\n✅ Command completed successfully!');
  } else {
    print('\n❌ Command failed with exit code: $exitCode');
    exit(exitCode);
  }
}

void showHelp() {
  print('''
Usage: dart run_tests.dart [option]

Options:
  all       Run all tests (default)
  helper    Run helper function tests only
  io        Run IO operation tests only  
  filter    Run FilterSubjects class tests only
  original  Run original core tests only
  main      Run main application demo
  help      Show this help message

Examples:
  dart run_tests.dart           # Run all tests
  dart run_tests.dart filter    # Run only FilterSubjects tests
  dart run_tests.dart main      # Run the main application demo
  dart run_tests.dart help      # Show this help
''');
}
