import '../io.dart';
import '../models/Subject.dart';
import '../helper.dart';


class FilterSubjects {
  List <Subject> adjacency_list;
  List <Subject> _possible_subjects = [];
  
  bool _finished_prerequisites(Subject subject) {
    for (var i = 0; i < subject.prerequisites.length; i++) {
      Subject? prerequisites_subject = get_subject(adjacency_list, subject.prerequisites[i]);
      if(prerequisites_subject == null) {
        // to-do throw an error subject_code is not correct
        //throw Exception('Error: Subject code "${subject.prerequisites[i]}" is not correct');
      }
      if (prerequisites_subject != null && !prerequisites_subject.status) {
        return false;
      }
    }
    return true;
  }

  bool traverse_single_subjects_tree(Subject subject) {
    if(!subject.status){
      return _finished_prerequisites(subject);
    }
    List<String> prerequisites_codes = subject.prerequisites;
    for(int i = 0; i < prerequisites_codes.length; i++){
      // to-do change the full_subjects
      Subject? prerequisite_subject = get_subject(adjacency_list, prerequisites_codes[i]);
      if(prerequisite_subject == null){
        throw Exception('Error: Subject code "${subject.prerequisites[i]}" is not correct');
      }
      traverse_single_subjects_tree(prerequisite_subject);
    }   
    // to-do check what to actually return if all prerequi
    return false;    
  }

  List<Subject> get_possible_subjects_v2() {
    print('get_possible_subjects_v2()');
    // to-do get subjects with no prerequisites
    List<Subject> initial_subjects = [];
    for(int i = 0;i < adjacency_list.length; i++){
      if(adjacency_list[i].prerequisites.length == 0) initial_subjects.add(adjacency_list[i]);
    }
    List<Subject> subjects_result = [];
    for(int i = 0; i < initial_subjects.length; i++){
      if(traverse_single_subjects_tree(initial_subjects[i])) {
        subjects_result.add(initial_subjects[i]);
      }
    }
    return subjects_result;
  }
 
  List<Subject> get_possible_subjects() {
    // to-do optimize to use depth first search instead
    for(int i = 0; i < adjacency_list.length; i++){
      if(!adjacency_list[i].status && _finished_prerequisites(adjacency_list[i])){
        _possible_subjects.add(adjacency_list[i]);
      }
    }
    return _possible_subjects;
  }

  FilterSubjects(this.adjacency_list) {
    adjacency_list.sort((subjA, subjB) => subjA.semester_number.compareTo(subjB.semester_number));
    //print_subjectArr(get_possible_subjects());
  }
}

void main() async {

  String file_path = 'data/GPE.txt';

  List<Subject> subjects = await loadSubjectsFromFile(file_path);

  // Test the get_subject method with sample data if file doesn't exist
  if (subjects.isEmpty) {
    print("Testing get_subject method with sample data:");

    // Create test subjects
    List<Subject> testSubjects = [
      Subject(
        semester_number: 1,
        status: true,
        code: 'CS101',
        englishName: 'Programming Fundamentals',
        arabicName: 'أساسيات البرمجة',
        dependencies: [],
        prerequisites: [],
        type: 'Core',
        departmentCode: 'CS',
        hours: 3,
      ),
      Subject(
        semester_number: 2,
        status: false,
        code: 'CS102',
        englishName: 'Data Structures',
        arabicName: 'هياكل البيانات',
        dependencies: [],
        prerequisites: ['CS101'],
        type: 'Core',
        departmentCode: 'CS',
        hours: 3,
      ),
    ];

    // Test get_subject method
    Subject? foundSubject = get_subject(testSubjects, 'CS101');
    if (foundSubject != null) {
      print("✅ Found subject: ${foundSubject.code} - ${foundSubject.englishName}");
    } else {
      print("❌ Subject CS101 not found");
    }

    Subject? notFoundSubject = get_subject(testSubjects, 'CS999');
    if (notFoundSubject == null) {
      print("✅ Correctly returned null for non-existent subject CS999");
    } else {
      print("❌ Should have returned null for CS999");
    }

    // Test FilterSubjects with sample data
    var filterSubjects = FilterSubjects(testSubjects);
    List<Subject> possibleSubjects = filterSubjects.get_possible_subjects();
    print("✅ Found ${possibleSubjects.length} possible subjects:");
    for (final subject in possibleSubjects) {
      print("   - ${subject.code}: ${subject.englishName}");
    }
  } else {
    var filterSubjects = FilterSubjects(subjects);
    List<Subject> possibleSubjects = filterSubjects.get_possible_subjects();
    print("Found ${possibleSubjects.length} possible subjects from file data");
  }
}





