class ExceddedHoursSumException implements Exception{
String msg;
ExceddedHoursSumException(this.msg);
@override
  String toString() {
    return "Excedded Hours Sum Exception ${this.msg}";
  }
}


class UnreachedGraduationHoursException implements Exception{
String msg;
UnreachedGraduationHoursException(this.msg);
@override
  String toString() {
    return "Unreached Graduation Hours Exception ${this.msg}";
  }
}


