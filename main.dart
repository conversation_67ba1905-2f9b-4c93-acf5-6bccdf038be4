import 'models/Subject.dart';
import 'models/Department.dart';
import 'models/Semesetr.dart';
import 'models/Lay7a.dart';
import 'functions/filtering_stage.dart';
import 'test/test_data.dart';
import 'exceptions/exceptions.dart';

void main() async {
  print("🎓 Graduation Algorithm System - Testing Implementation");
  print("=" * 60);

  // // Test 1: Basic Subject Operations
  // await testSubjectOperations();

  // // Test 2: Filtering and Prerequisites
  // await testFilteringOperations();

  // // Test 3: Semester Management
  // await testSemesterOperations();

  // // Test 4: Department and Lay7a Operations
  // await testDepartmentAndLay7aOperations();

  // Test 5: Exception Handling
  await testExceptionHandling();

  // // Test 6: Real-world Scenario Simulation
  // await testRealWorldScenario();

  print("\n🎉 All tests completed successfully!");
}

/// Test basic subject operations including creation, cloning, and JSON serialization
Future<void> testSubjectOperations() async {
  print("\n📚 Testing Subject Operations");
  print("-" * 40);

  // Create a test subject
  final subject = Subject(
    semester_number: 1,
    status: false,
    code: 'CS101',
    englishName: 'Introduction to Computer Science',
    arabicName: 'مقدمة في علوم الحاسوب',
    dependencies: [],
    prerequisites: [],
    type: 'Core',
    departmentCode: 'CS',
    hours: 3,
  );

  print("✅ Created subject: ${subject.code} - ${subject.englishName}");
  print("   Arabic Name: ${subject.arabicName}");
  print("   Hours: ${subject.hours}, Status: ${subject.status ? 'Completed' : 'Not Completed'}");

  // Test cloning
  final clonedSubject = subject.clone();
  clonedSubject.status = true;

  print("✅ Cloned subject and changed status");
  print("   Original status: ${subject.status}");
  print("   Cloned status: ${clonedSubject.status}");

  // Test JSON serialization
  final json = subject.toJson();
  final fromJson = Subject.fromJson(json);

  print("✅ JSON serialization test passed");
  print("   Serialized and deserialized subject code: ${fromJson.code}");
}

/// Test filtering operations and prerequisite checking
Future<void> testFilteringOperations() async {
  print("\n🔍 Testing Filtering Operations");
  print("-" * 40);

  // Test get_subject_by_code function
  print("Testing get_subject_by_code function:");
  final foundSubject = get_subject_by_code('S1');
  if (foundSubject != null) {
    print("✅ Found subject S1: ${foundSubject.englishName}");
  } else {
    print("❌ Subject S1 not found");
  }

  final notFoundSubject = get_subject_by_code('NONEXISTENT');
  if (notFoundSubject == null) {
    print("✅ Correctly returned null for non-existent subject");
  } else {
    print("❌ Should have returned null for non-existent subject");
  }

  // Test finished_prerequisites function
  print("\nTesting finished_prerequisites function:");
  final subjectWithNoPrereqs = testSubjects[0]; // S1 has no prerequisites
  final hasFinishedPrereqs = finished_prerequisites(subjectWithNoPrereqs);
  print("✅ Subject ${subjectWithNoPrereqs.code} prerequisites check: $hasFinishedPrereqs");

  // Test get_possible_subjects function
  print("\nTesting get_possible_subjects function:");
  final possibleSubjects = get_possible_subjects(testSubjects);
  print("✅ Found ${possibleSubjects.length} possible subjects:");
  for (final subject in possibleSubjects) {
    print("   - ${subject.code}: ${subject.englishName} (${subject.status ? 'Completed' : 'Available'})");
  }
}

/// Test semester management operations
Future<void> testSemesterOperations() async {
  print("\n📅 Testing Semester Operations");
  print("-" * 40);

  // Create a semester
  final semester = Semester(number: 1);
  print("✅ Created semester ${semester.number}");
  print("   Initial hours: ${semester.hours}");
  print("   Initial subjects count: ${semester.subjects.length}");

  // Add subjects to semester
  final subject1 = testSubjects[0].clone();
  final subject2 = testSubjects[2].clone();

  semester.addSubject(subject1);
  semester.addSubject(subject2);

  print("✅ Added subjects to semester:");
  print("   Total hours: ${semester.hours}");
  print("   Subjects count: ${semester.subjects.length}");
  for (final subject in semester.subjects) {
    print("   - ${subject.code}: ${subject.englishName} (${subject.hours} hours)");
  }

  // Test semester cloning
  final clonedSemester = Semester.clone(semester);
  clonedSemester.incrementNumber();

  print("✅ Cloned semester:");
  print("   Original semester number: ${semester.number}");
  print("   Cloned semester number: ${clonedSemester.number}");
  print("   Cloned semester hours: ${clonedSemester.hours}");

  // Test clearing semester
  clonedSemester.clear();
  print("✅ Cleared cloned semester:");
  print("   Subjects after clear: ${clonedSemester.subjects.length}");
}

/// Test department and lay7a (curriculum) operations
Future<void> testDepartmentAndLay7aOperations() async {
  print("\n🏛️ Testing Department and Lay7a Operations");
  print("-" * 40);

  // Create a department
  final department = Department(
    total_hours: 132,
    code: 'CS',
    arabicName: 'قسم علوم الحاسوب',
    englishName: 'Computer Science Department',
    departmentType: 'Engineering',
    collegeType: 'Engineering College',
  );

  print("✅ Created department:");
  print("   Code: ${department.code}");
  print("   English Name: ${department.englishName}");
  print("   Arabic Name: ${department.arabicName}");
  print("   Total Hours Required: ${department.total_hours}");
  print("   Department Type: ${department.departmentType}");
  print("   College Type: ${department.collegeType}");

  // Create a lay7a (curriculum)
  final lay7a = Lay7a(
    year: 2024,
    subjects: testSubjects,
    collegeType: department.collegeType,
    department: department,
  );

  print("\n✅ Created Lay7a (Curriculum):");
  print("   Year: ${lay7a.year}");
  print("   College Type: ${lay7a.collegeType}");
  print("   Department: ${lay7a.department.englishName}");
  print("   Total Subjects: ${lay7a.subjects.length}");

  // Calculate total hours in curriculum
  int totalHours = 0;
  int completedHours = 0;
  for (final subject in lay7a.subjects) {
    totalHours += subject.hours;
    if (subject.status) {
      completedHours += subject.hours;
    }
  }

  print("   Total Hours in Curriculum: $totalHours");
  print("   Completed Hours: $completedHours");
  print("   Remaining Hours: ${totalHours - completedHours}");
  print("   Progress: ${((completedHours / totalHours) * 100).toStringAsFixed(1)}%");
}

/// Test exception handling
Future<void> testExceptionHandling() async {
  print("\n⚠️ Testing Exception Handling");
  print("-" * 40);

  // Test ExceddedHoursSumException
  try {
    throw ExceddedHoursSumException("Student attempted to register for 25 hours in one semester (max: 18)");
  } catch (e) {
    print("✅ Caught ExceddedHoursSumException:");
    print("   $e");
  }

  // Test UnreachedGraduationHoursException
  try {
    throw UnreachedGraduationHoursException("Student has only 120 hours completed (required: 132)");
  } catch (e) {
    print("✅ Caught UnreachedGraduationHoursException:");
    print("   $e");
  }

  // Simulate a real scenario where exceptions might be thrown
  print("\n📊 Simulating semester hour validation:");
  final maxHoursPerSemester = 18;
  final semester = Semester(number: 1);

  // Add subjects that would exceed the limit
  final heavySubjects = [
    testSubjects[0].clone(), // 3 hours
    testSubjects[1].clone(), // 3 hours
    testSubjects[2].clone(), // 3 hours
  ];

  // Simulate adding subjects with hour checking
  for (final subject in heavySubjects) {
    semester.addSubject(subject);
    if (semester.hours > maxHoursPerSemester) {
      print("⚠️ Would throw ExceddedHoursSumException:");
      print("   Current hours: ${semester.hours}, Max allowed: $maxHoursPerSemester");
      break;
    } else {
      print("✅ Added ${subject.code} (${subject.hours} hours). Total: ${semester.hours}");
    }
  }
}

/// Test a real-world graduation planning scenario
Future<void> testRealWorldScenario() async {
  print("\n🎯 Real-World Graduation Planning Scenario");
  print("-" * 50);

  print("📋 Scenario: A Computer Science student planning their next semester");
  print("   Current situation: Some subjects completed, planning next semester");

  // Create a more realistic set of subjects
  final realSubjects = [
    Subject(
      semester_number: 1,
      status: true, // Completed
      code: 'MATH101',
      englishName: 'Calculus I',
      arabicName: 'التفاضل والتكامل الأول',
      dependencies: ['MATH102'],
      prerequisites: [],
      type: 'Core',
      departmentCode: 'MATH',
      hours: 3,
    ),
    Subject(
      semester_number: 2,
      status: false, // Not completed
      code: 'MATH102',
      englishName: 'Calculus II',
      arabicName: 'التفاضل والتكامل الثاني',
      dependencies: ['PHYS101'],
      prerequisites: ['MATH101'],
      type: 'Core',
      departmentCode: 'MATH',
      hours: 3,
    ),
    Subject(
      semester_number: 2,
      status: false,
      code: 'CS101',
      englishName: 'Programming Fundamentals',
      arabicName: 'أساسيات البرمجة',
      dependencies: ['CS102'],
      prerequisites: [],
      type: 'Core',
      departmentCode: 'CS',
      hours: 4,
    ),
    Subject(
      semester_number: 3,
      status: false,
      code: 'CS102',
      englishName: 'Data Structures',
      arabicName: 'هياكل البيانات',
      dependencies: [],
      prerequisites: ['CS101'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 3,
      status: false,
      code: 'PHYS101',
      englishName: 'Physics I',
      arabicName: 'الفيزياء الأولى',
      dependencies: [],
      prerequisites: ['MATH102'],
      type: 'Core',
      departmentCode: 'PHYS',
      hours: 3,
    ),
  ];

  print("\n📚 Available subjects in curriculum:");
  for (final subject in realSubjects) {
    final statusIcon = subject.status ? '✅' : '⏳';
    print("   $statusIcon ${subject.code}: ${subject.englishName} (${subject.hours}h)");
    if (subject.prerequisites.isNotEmpty) {
      print("      Prerequisites: ${subject.prerequisites.join(', ')}");
    }
  }

  // Simulate the filtering process
  print("\n🔍 Determining which subjects can be taken next semester:");

  // Create a custom function to work with our real subjects
  List<Subject> getPossibleSubjectsFromList(List<Subject> subjects) {
    List<Subject> possible = [];
    for (final subject in subjects) {
      if (!subject.status) { // Not completed yet
        bool canTake = true;
        for (final prereqCode in subject.prerequisites) {
          final prereqSubject = subjects.firstWhere(
            (s) => s.code == prereqCode,
            orElse: () => Subject(
              semester_number: 0,
              status: false,
              code: '',
              englishName: '',
              arabicName: '',
              dependencies: [],
              prerequisites: [],
              type: '',
              departmentCode: '',
              hours: 0,
            ),
          );
          if (prereqSubject.code.isNotEmpty && !prereqSubject.status) {
            canTake = false;
            break;
          }
        }
        if (canTake) {
          possible.add(subject);
        }
      }
    }
    return possible;
  }

  final possibleNextSemester = getPossibleSubjectsFromList(realSubjects);

  print("✅ Subjects available for next semester:");
  int totalHours = 0;
  for (final subject in possibleNextSemester) {
    print("   📖 ${subject.code}: ${subject.englishName}");
    print("      Hours: ${subject.hours}, Department: ${subject.departmentCode}");
    totalHours += subject.hours;
  }

  print("\n📊 Semester Planning Summary:");
  print("   Available subjects: ${possibleNextSemester.length}");
  print("   Total available hours: $totalHours");
  print("   Recommended load: ${totalHours > 15 ? '15-18 hours (select subset)' : '$totalHours hours (all subjects)'}");

  // Simulate semester creation
  final nextSemester = Semester(number: 2);
  final maxHours = 15;

  print("\n📅 Creating next semester plan (max $maxHours hours):");
  for (final subject in possibleNextSemester) {
    if (nextSemester.hours + subject.hours <= maxHours) {
      nextSemester.addSubject(subject);
      print("   ✅ Added: ${subject.code} (${subject.hours}h)");
    } else {
      print("   ⏭️ Deferred: ${subject.code} (would exceed hour limit)");
    }
  }

  print("\n🎯 Final semester plan:");
  print("   Semester: ${nextSemester.number}");
  print("   Total hours: ${nextSemester.hours}");
  print("   Subjects enrolled: ${nextSemester.subjects.length}");
}

