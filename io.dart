import 'dart:io';
import 'dart:convert';
import 'models/Subject.dart';

// Save a list of subjects to a file
Future<void> saveSubjectsToFile(List<Subject> subjects, String filePath) async {
  
  final file = File(filePath);
  if (await file.exists()) {
    print('File already exists');
  }else{
    // Convert the list to JSON
    String json = jsonEncode(subjects.map((subject) => subject.toJson()).toList());

    //  Write the JSON string to the file
    await file.writeAsString(json);
     print('Subjects saved to file.');
  }
  
}

// Load a list of subjects from a file
Future<List<Subject>> loadSubjectsFromFile(String filePath) async {
  final file = File(filePath);

  // Check if the file exists
  if (await file.exists()) {
    // Read the JSON string from the file
    String json = await file.readAsString();

    // Decode the JSON string into a list of subjects
    List<dynamic> jsonData = jsonDecode(json);
    return jsonData.map((data) => Subject.fromJson(data)).toList();
  } else {
    print('File does not exist.');
    return [];
  }
}