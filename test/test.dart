import 'package:test/test.dart';
import '../functions/filtering_stage.dart'; 
import '../models/Subject.dart';
import './test_data.dart';

void main() {
  group('get_possible_subjects', () {
    test('returns empty list when input list is empty', () {
      expect(get_possible_subjects([]), isEmpty);
    });

    test('returns subjects with no prerequisites', () {
      final subject1 = testSubjects[0];
      final subject2 = testSubjects[1];
      final subject3 = testSubjects[2];

      expect(get_possible_subjects([subject1, subject2]), [subject3]);
    });

    test('returns subjects with finished prerequisites', () {
      final subject1 = testSubjects[0];
      final subject2 = testSubjects[1];
      final subject3 = testSubjects[2];
      // final subject1 = Subject(
      //   semester_number: 1,
      //   status: false,
      //   code: 'S1',
      //   englishName: 'Subject 1',
      //   arabicName: 'المادة 1',
      //   dependencies: [],
      //   prerequisites: [],
      //   type: 'type',
      //   departmentCode: 'dept',
      //   hours: 3,
      // );
      // final subject2 = Subject(
      //   semester_number: 2,
      //   status: true,
      //   code: 'S2',
      //   englishName: 'Subject 2',
      //   arabicName: 'المادة 2',
      //   dependencies: ['S1'],
      //   prerequisites: ['S1'],
      //   type: 'type',
      //   departmentCode: 'dept',
      //   hours: 3,
      // );

      expect(get_possible_subjects([subject1, subject2]), [subject1, subject2]);
    });

    // test('does not return subjects with unmet prerequisites', () {
    //   final subject1 = Subject(
    //     semester_number: 1,
    //     status: true,
    //     code: 'S1',
    //     englishName: 'Subject 1',
    //     arabicName: 'المادة 1',
    //     dependencies: [],
    //     prerequisites: [],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );
    //   final subject2 = Subject(
    //     semester_number: 2,
    //     status: true,
    //     code: 'S2',
    //     englishName: 'Subject 2',
    //     arabicName: 'المادة 2',
    //     dependencies: ['S1'],
    //     prerequisites: ['S1'],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );

    //   expect(get_possible_subjects([subject2, subject1]), [subject1]);
    // });

    // test('handles circular dependencies', () {
    //   final subject1 = Subject(
    //     semester_number: 1,
    //     status: true,
    //     code: 'S1',
    //     englishName: 'Subject 1',
    //     arabicName: 'المادة 1',
    //     dependencies: ['S2'],
    //     prerequisites: [],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );
    //   final subject2 = Subject(
    //     semester_number: 2,
    //     status: true,
    //     code: 'S2',
    //     englishName: 'Subject 2',
    //     arabicName: 'المادة 2',
    //     dependencies: ['S1'],
    //     prerequisites: [],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );

    //   expect(get_possible_subjects([subject1, subject2]), isEmpty);
    // });

    // test('handles duplicate subjects', () {
    //   final subject1 = Subject(
    //     semester_number: 1,
    //     status: true,
    //     code: 'S1',
    //     englishName: 'Subject 1',
    //     arabicName: 'المادة 1',
    //     dependencies: [],
    //     prerequisites: [],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );
    //   final subject2 = Subject(
    //     semester_number: 2,
    //     status: true,
    //     code: 'S2',
    //     englishName: 'Subject 2',
    //     arabicName: 'المادة 2',
    //     dependencies: ['S1'],
    //     prerequisites: [],
    //     type: 'type',
    //     departmentCode: 'dept',
    //     hours: 3,
    //   );

    //   expect(get_possible_subjects([subject1, subject1, subject2]), [subject1]);
    // });


  });
}