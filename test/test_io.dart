import 'package:test/test.dart';
import 'dart:io';
import '../io.dart';
import '../models/Subject.dart';

void main() {
  group('IO Functions Tests', () {
    late List<Subject> testSubjects;
    late String testFilePath;
    late String nonExistentFilePath;

    setUp(() {
      testSubjects = [
        Subject(
          semester_number: 1,
          status: true,
          code: 'CS101',
          englishName: 'Programming Fundamentals',
          arabicName: 'أساسيات البرمجة',
          dependencies: ['CS102'],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 2,
          status: false,
          code: 'CS102',
          englishName: 'Data Structures',
          arabicName: 'هياكل البيانات',
          dependencies: [],
          prerequisites: ['CS101'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 4,
        ),
        Subject(
          semester_number: 1,
          status: true,
          code: 'MATH101',
          englishName: 'Calculus I',
          arabicName: 'التفاضل والتكامل الأول',
          dependencies: ['MATH102'],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 3,
        ),
      ];

      testFilePath = 'test_subjects.json';
      nonExistentFilePath = 'non_existent_file.json';
    });

    tearDown(() async {
      // Clean up test files
      final testFile = File(testFilePath);
      if (await testFile.exists()) {
        await testFile.delete();
      }
    });

    group('saveSubjectsToFile function', () {
      test('should create new file and save subjects successfully', () async {
        // Ensure file doesn't exist
        final file = File(testFilePath);
        if (await file.exists()) {
          await file.delete();
        }

        await saveSubjectsToFile(testSubjects, testFilePath);

        // Verify file was created
        expect(await file.exists(), isTrue);

        // Verify file content
        final content = await file.readAsString();
        expect(content, isNotEmpty);
        expect(content, contains('CS101'));
        expect(content, contains('Programming Fundamentals'));
        expect(content, contains('أساسيات البرمجة'));
      });

      test('should not overwrite existing file', () async {
        // Create file first
        await saveSubjectsToFile(testSubjects, testFilePath);
        final originalContent = await File(testFilePath).readAsString();

        // Try to save again with different data
        final differentSubjects = [
          Subject(
            semester_number: 5,
            status: false,
            code: 'DIFFERENT',
            englishName: 'Different Subject',
            arabicName: 'مادة مختلفة',
            dependencies: [],
            prerequisites: [],
            type: 'Elective',
            departmentCode: 'DIFF',
            hours: 2,
          ),
        ];

        await saveSubjectsToFile(differentSubjects, testFilePath);

        // Verify content hasn't changed
        final newContent = await File(testFilePath).readAsString();
        expect(newContent, equals(originalContent));
        expect(newContent, isNot(contains('DIFFERENT')));
      });

      test('should handle empty subject list', () async {
        await saveSubjectsToFile([], testFilePath);

        final file = File(testFilePath);
        expect(await file.exists(), isTrue);

        final content = await file.readAsString();
        expect(content, equals('[]'));
      });

      test('should handle subjects with special characters', () async {
        final specialSubjects = [
          Subject(
            semester_number: 1,
            status: false,
            code: 'SPEC-101',
            englishName: 'Special "Characters" & Symbols',
            arabicName: 'رموز خاصة "ونصوص" ومحارف',
            dependencies: ['SPEC-102'],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'SPEC',
            hours: 3,
          ),
        ];

        await saveSubjectsToFile(specialSubjects, testFilePath);

        final file = File(testFilePath);
        expect(await file.exists(), isTrue);

        final content = await file.readAsString();
        expect(content, contains('SPEC-101'));
        expect(content, contains('Special \\"Characters\\" & Symbols'));
        expect(content, contains('رموز خاصة'));
      });

      test('should handle subjects with unicode characters', () async {
        final unicodeSubjects = [
          Subject(
            semester_number: 1,
            status: false,
            code: '🎓CS101',
            englishName: 'Programming with Emojis 🚀',
            arabicName: 'البرمجة مع الرموز التعبيرية 💻',
            dependencies: [],
            prerequisites: [],
            type: 'Fun',
            departmentCode: 'EMO',
            hours: 3,
          ),
        ];

        await saveSubjectsToFile(unicodeSubjects, testFilePath);

        final file = File(testFilePath);
        expect(await file.exists(), isTrue);

        final content = await file.readAsString();
        expect(content, contains('🎓CS101'));
        expect(content, contains('🚀'));
        expect(content, contains('💻'));
      });
    });

    group('loadSubjectsFromFile function', () {
      test('should load subjects from existing file correctly', () async {
        // First save subjects
        await saveSubjectsToFile(testSubjects, testFilePath);

        // Then load them
        final loadedSubjects = await loadSubjectsFromFile(testFilePath);

        expect(loadedSubjects.length, equals(testSubjects.length));

        // Verify first subject
        expect(loadedSubjects[0].code, equals('CS101'));
        expect(loadedSubjects[0].englishName, equals('Programming Fundamentals'));
        expect(loadedSubjects[0].arabicName, equals('أساسيات البرمجة'));
        expect(loadedSubjects[0].status, equals(true));
        expect(loadedSubjects[0].hours, equals(3));
        expect(loadedSubjects[0].semester_number, equals(1));
        expect(loadedSubjects[0].type, equals('Core'));
        expect(loadedSubjects[0].departmentCode, equals('CS'));
        expect(loadedSubjects[0].dependencies, equals(['CS102']));
        expect(loadedSubjects[0].prerequisites, equals([]));

        // Verify second subject
        expect(loadedSubjects[1].code, equals('CS102'));
        expect(loadedSubjects[1].status, equals(false));
        expect(loadedSubjects[1].prerequisites, equals(['CS101']));
      });

      test('should return empty list for non-existent file', () async {
        final loadedSubjects = await loadSubjectsFromFile(nonExistentFilePath);
        expect(loadedSubjects, isEmpty);
      });

      test('should handle empty file correctly', () async {
        // Create empty JSON file
        final file = File(testFilePath);
        await file.writeAsString('[]');

        final loadedSubjects = await loadSubjectsFromFile(testFilePath);
        expect(loadedSubjects, isEmpty);
      });

      test('should preserve all subject properties', () async {
        // Create subject with all possible values
        final complexSubject = Subject(
          semester_number: 8,
          status: true,
          code: 'COMPLEX999',
          englishName: 'Complex Subject with Long Name',
          arabicName: 'مادة معقدة باسم طويل جداً ومفصل',
          dependencies: ['DEP1', 'DEP2', 'DEP3'],
          prerequisites: ['PREREQ1', 'PREREQ2'],
          type: 'Advanced Elective',
          departmentCode: 'ADVANCED',
          hours: 6,
        );

        await saveSubjectsToFile([complexSubject], testFilePath);
        final loadedSubjects = await loadSubjectsFromFile(testFilePath);

        expect(loadedSubjects.length, equals(1));
        final loaded = loadedSubjects[0];

        expect(loaded.semester_number, equals(8));
        expect(loaded.status, equals(true));
        expect(loaded.code, equals('COMPLEX999'));
        expect(loaded.englishName, equals('Complex Subject with Long Name'));
        expect(loaded.arabicName, equals('مادة معقدة باسم طويل جداً ومفصل'));
        expect(loaded.dependencies, equals(['DEP1', 'DEP2', 'DEP3']));
        expect(loaded.prerequisites, equals(['PREREQ1', 'PREREQ2']));
        expect(loaded.type, equals('Advanced Elective'));
        expect(loaded.departmentCode, equals('ADVANCED'));
        expect(loaded.hours, equals(6));
      });

      test('should handle malformed JSON gracefully', () async {
        // Create file with invalid JSON
        final file = File(testFilePath);
        await file.writeAsString('{ invalid json }');

        expect(
          () async => await loadSubjectsFromFile(testFilePath),
          throwsA(isA<FormatException>()),
        );
      });

      test('should handle file with missing required fields', () async {
        // Create JSON with missing required fields
        final file = File(testFilePath);
        await file.writeAsString('[{"code": "INCOMPLETE"}]');

        expect(
          () async => await loadSubjectsFromFile(testFilePath),
          throwsA(isA<TypeError>()),
        );
      });
    });

    group('Round-trip tests (save and load)', () {
      test('should maintain data integrity through save-load cycle', () async {
        // Save original subjects
        await saveSubjectsToFile(testSubjects, testFilePath);

        // Load subjects
        final loadedSubjects = await loadSubjectsFromFile(testFilePath);

        // Verify all subjects are identical
        expect(loadedSubjects.length, equals(testSubjects.length));

        for (int i = 0; i < testSubjects.length; i++) {
          final original = testSubjects[i];
          final loaded = loadedSubjects[i];

          expect(loaded.code, equals(original.code));
          expect(loaded.englishName, equals(original.englishName));
          expect(loaded.arabicName, equals(original.arabicName));
          expect(loaded.status, equals(original.status));
          expect(loaded.hours, equals(original.hours));
          expect(loaded.semester_number, equals(original.semester_number));
          expect(loaded.type, equals(original.type));
          expect(loaded.departmentCode, equals(original.departmentCode));
          expect(loaded.dependencies, equals(original.dependencies));
          expect(loaded.prerequisites, equals(original.prerequisites));
        }
      });

      test('should handle multiple save-load cycles', () async {
        var currentSubjects = testSubjects;

        for (int cycle = 0; cycle < 5; cycle++) {
          await saveSubjectsToFile(currentSubjects, testFilePath);
          currentSubjects = await loadSubjectsFromFile(testFilePath);

          expect(currentSubjects.length, equals(testSubjects.length));
          expect(currentSubjects[0].code, equals('CS101'));
          expect(currentSubjects[1].code, equals('CS102'));
          expect(currentSubjects[2].code, equals('MATH101'));
        }
      });

      test('should preserve Arabic text encoding', () async {
        final arabicSubjects = [
          Subject(
            semester_number: 1,
            status: false,
            code: 'عرب101',
            englishName: 'Arabic Language',
            arabicName: 'اللغة العربية الفصحى والنحو والصرف',
            dependencies: ['عرب102'],
            prerequisites: [],
            type: 'إجباري',
            departmentCode: 'عربي',
            hours: 3,
          ),
        ];

        await saveSubjectsToFile(arabicSubjects, testFilePath);
        final loadedSubjects = await loadSubjectsFromFile(testFilePath);

        expect(loadedSubjects.length, equals(1));
        expect(loadedSubjects[0].code, equals('عرب101'));
        expect(loadedSubjects[0].arabicName, equals('اللغة العربية الفصحى والنحو والصرف'));
        expect(loadedSubjects[0].dependencies, equals(['عرب102']));
        expect(loadedSubjects[0].type, equals('إجباري'));
        expect(loadedSubjects[0].departmentCode, equals('عربي'));
      });
    });

    group('Performance and edge cases', () {
      test('should handle large number of subjects', () async {
        // Create large list of subjects
        final largeSubjectList = <Subject>[];
        for (int i = 0; i < 100; i++) {
          largeSubjectList.add(Subject(
            semester_number: (i % 8) + 1,
            status: i % 2 == 0,
            code: 'LARGE$i',
            englishName: 'Large Subject $i',
            arabicName: 'مادة كبيرة $i',
            dependencies: i > 0 ? ['LARGE${i-1}'] : [],
            prerequisites: i > 0 ? ['LARGE${i-1}'] : [],
            type: 'Core',
            departmentCode: 'LARGE',
            hours: (i % 4) + 1,
          ));
        }

        await saveSubjectsToFile(largeSubjectList, testFilePath);
        final loadedSubjects = await loadSubjectsFromFile(testFilePath);

        expect(loadedSubjects.length, equals(100));
        expect(loadedSubjects[0].code, equals('LARGE0'));
        expect(loadedSubjects[99].code, equals('LARGE99'));
      });

      test('should handle very long file paths', () async {
        final longPath = 'very_long_file_name_' + 'a' * 100 + '.json';
        
        await saveSubjectsToFile(testSubjects, longPath);
        final loadedSubjects = await loadSubjectsFromFile(longPath);

        expect(loadedSubjects.length, equals(testSubjects.length));

        // Clean up
        final file = File(longPath);
        if (await file.exists()) {
          await file.delete();
        }
      });
    });
  });
}
