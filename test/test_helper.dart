import 'package:test/test.dart';
import '../helper.dart';
import '../models/Subject.dart';
import '../models/Department.dart';

void main() {
  group('Helper Functions Tests', () {
    late List<Subject> testSubjects;
    late List<Department> testDepartments;

    setUp(() {
      testSubjects = [
        Subject(
          semester_number: 1,
          status: true,
          code: 'CS101',
          englishName: 'Programming Fundamentals',
          arabicName: 'أساسيات البرمجة',
          dependencies: [],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 2,
          status: false,
          code: 'CS102',
          englishName: 'Data Structures',
          arabicName: 'هياكل البيانات',
          dependencies: [],
          prerequisites: ['CS101'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 1,
          status: true,
          code: 'MATH101',
          englishName: 'Calculus I',
          arabicName: 'التفاضل والتكامل الأول',
          dependencies: [],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 4,
        ),
      ];

      testDepartments = [
        Department(
          total_hours: 132,
          code: 'CS',
          arabicName: 'قسم علوم الحاسوب',
          englishName: 'Computer Science Department',
          departmentType: 'Engineering',
          collegeType: 'Engineering College',
        ),
        Department(
          total_hours: 128,
          code: 'MATH',
          arabicName: 'قسم الرياضيات',
          englishName: 'Mathematics Department',
          departmentType: 'Science',
          collegeType: 'Science College',
        ),
      ];
    });

    group('get_subject function', () {
      test('should find existing subject by exact code match', () {
        final result = get_subject(testSubjects, 'CS101');
        expect(result, isNotNull);
        expect(result!.code, equals('CS101'));
        expect(result.englishName, equals('Programming Fundamentals'));
        expect(result.arabicName, equals('أساسيات البرمجة'));
      });

      test('should return null for non-existent subject code', () {
        final result = get_subject(testSubjects, 'CS999');
        expect(result, isNull);
      });

      test('should be case-sensitive', () {
        final result = get_subject(testSubjects, 'cs101');
        expect(result, isNull);
      });

      test('should return null for empty string', () {
        final result = get_subject(testSubjects, '');
        expect(result, isNull);
      });

      test('should handle empty subject list', () {
        final result = get_subject([], 'CS101');
        expect(result, isNull);
      });

      test('should return first match when duplicates exist', () {
        // Add a duplicate subject
        final duplicate = Subject(
          semester_number: 3,
          status: false,
          code: 'CS101',
          englishName: 'Advanced Programming',
          arabicName: 'البرمجة المتقدمة',
          dependencies: [],
          prerequisites: [],
          type: 'Elective',
          departmentCode: 'CS',
          hours: 4,
        );
        testSubjects.add(duplicate);

        final result = get_subject(testSubjects, 'CS101');
        expect(result, isNotNull);
        expect(result!.englishName, equals('Programming Fundamentals')); // First one
        expect(result.semester_number, equals(1)); // First one
      });

      test('should handle subjects with special characters in code', () {
        final specialSubject = Subject(
          semester_number: 1,
          status: false,
          code: 'CS-101A',
          englishName: 'Special Programming',
          arabicName: 'برمجة خاصة',
          dependencies: [],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        );
        testSubjects.add(specialSubject);

        final result = get_subject(testSubjects, 'CS-101A');
        expect(result, isNotNull);
        expect(result!.englishName, equals('Special Programming'));
      });

      test('should handle Arabic characters in subject codes', () {
        final arabicSubject = Subject(
          semester_number: 1,
          status: false,
          code: 'عرب101',
          englishName: 'Arabic Language',
          arabicName: 'اللغة العربية',
          dependencies: [],
          prerequisites: [],
          type: 'General',
          departmentCode: 'ARAB',
          hours: 2,
        );
        testSubjects.add(arabicSubject);

        final result = get_subject(testSubjects, 'عرب101');
        expect(result, isNotNull);
        expect(result!.englishName, equals('Arabic Language'));
      });

      test('should handle whitespace in codes correctly', () {
        final result1 = get_subject(testSubjects, ' CS101');
        expect(result1, isNull);

        final result2 = get_subject(testSubjects, 'CS101 ');
        expect(result2, isNull);

        final result3 = get_subject(testSubjects, ' CS101 ');
        expect(result3, isNull);
      });
    });

    group('print_subjectArr function', () {
      test('should handle empty list without errors', () {
        expect(() => print_subjectArr([]), returnsNormally);
      });

      test('should handle single subject without errors', () {
        expect(() => print_subjectArr([testSubjects[0]]), returnsNormally);
      });

      test('should handle multiple subjects without errors', () {
        expect(() => print_subjectArr(testSubjects), returnsNormally);
      });

      test('should handle subjects with null or empty fields', () {
        final subjectWithEmptyFields = Subject(
          semester_number: 0,
          status: false,
          code: '',
          englishName: '',
          arabicName: '',
          dependencies: [],
          prerequisites: [],
          type: '',
          departmentCode: '',
          hours: 0,
        );
        
        expect(() => print_subjectArr([subjectWithEmptyFields]), returnsNormally);
      });
    });

    group('print_departmentArr function', () {
      test('should handle empty list without errors', () {
        expect(() => print_departmentArr([]), returnsNormally);
      });

      test('should handle single department without errors', () {
        expect(() => print_departmentArr([testDepartments[0]]), returnsNormally);
      });

      test('should handle multiple departments without errors', () {
        expect(() => print_departmentArr(testDepartments), returnsNormally);
      });

      test('should handle departments with empty fields', () {
        final emptyDepartment = Department(
          total_hours: 0,
          code: '',
          arabicName: '',
          englishName: '',
          departmentType: '',
          collegeType: '',
        );
        
        expect(() => print_departmentArr([emptyDepartment]), returnsNormally);
      });
    });

    group('Integration tests', () {
      test('get_subject should work with realistic subject data', () {
        // Test with all subjects in the test data
        for (final subject in testSubjects) {
          final found = get_subject(testSubjects, subject.code);
          expect(found, isNotNull);
          expect(found!.code, equals(subject.code));
          expect(found.englishName, equals(subject.englishName));
          expect(found.arabicName, equals(subject.arabicName));
        }
      });

      test('should handle large subject lists efficiently', () {
        // Create a large list of subjects
        final largeSubjectList = <Subject>[];
        for (int i = 0; i < 1000; i++) {
          largeSubjectList.add(Subject(
            semester_number: (i % 8) + 1,
            status: i % 2 == 0,
            code: 'SUBJ$i',
            englishName: 'Subject $i',
            arabicName: 'مادة $i',
            dependencies: [],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'DEPT${i % 10}',
            hours: (i % 4) + 1,
          ));
        }

        // Test finding subjects at different positions
        final firstSubject = get_subject(largeSubjectList, 'SUBJ0');
        expect(firstSubject, isNotNull);
        expect(firstSubject!.englishName, equals('Subject 0'));

        final middleSubject = get_subject(largeSubjectList, 'SUBJ500');
        expect(middleSubject, isNotNull);
        expect(middleSubject!.englishName, equals('Subject 500'));

        final lastSubject = get_subject(largeSubjectList, 'SUBJ999');
        expect(lastSubject, isNotNull);
        expect(lastSubject!.englishName, equals('Subject 999'));

        final nonExistent = get_subject(largeSubjectList, 'SUBJ1000');
        expect(nonExistent, isNull);
      });

      test('should maintain data integrity during searches', () {
        final originalLength = testSubjects.length;
        final originalFirstSubject = testSubjects[0];

        // Perform multiple searches
        for (int i = 0; i < 10; i++) {
          get_subject(testSubjects, 'CS101');
          get_subject(testSubjects, 'NONEXISTENT');
        }

        // Verify list hasn't been modified
        expect(testSubjects.length, equals(originalLength));
        expect(testSubjects[0].code, equals(originalFirstSubject.code));
        expect(testSubjects[0].englishName, equals(originalFirstSubject.englishName));
      });
    });

    group('Edge cases and error handling', () {
      test('should handle null safety correctly', () {
        // These tests ensure the function handles edge cases without throwing
        expect(() => get_subject(testSubjects, 'CS101'), returnsNormally);
        expect(() => get_subject([], 'CS101'), returnsNormally);
        expect(() => get_subject(testSubjects, ''), returnsNormally);
      });

      test('should handle very long subject codes', () {
        final longCode = 'A' * 1000;
        final longCodeSubject = Subject(
          semester_number: 1,
          status: false,
          code: longCode,
          englishName: 'Long Code Subject',
          arabicName: 'مادة برمز طويل',
          dependencies: [],
          prerequisites: [],
          type: 'Test',
          departmentCode: 'TEST',
          hours: 1,
        );
        testSubjects.add(longCodeSubject);

        final result = get_subject(testSubjects, longCode);
        expect(result, isNotNull);
        expect(result!.englishName, equals('Long Code Subject'));
      });

      test('should handle unicode characters in subject codes', () {
        final unicodeSubject = Subject(
          semester_number: 1,
          status: false,
          code: '🎓📚💻',
          englishName: 'Unicode Subject',
          arabicName: 'مادة يونيكود',
          dependencies: [],
          prerequisites: [],
          type: 'Special',
          departmentCode: 'UNI',
          hours: 3,
        );
        testSubjects.add(unicodeSubject);

        final result = get_subject(testSubjects, '🎓📚💻');
        expect(result, isNotNull);
        expect(result!.englishName, equals('Unicode Subject'));
      });
    });
  });
}
