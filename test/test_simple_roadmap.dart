import 'package:test/test.dart';
import '../models/SimpleRoadMap.dart';
import '../models/Subject.dart';
import '../models/Semesetr.dart';

void main() {
  group('SimpleRoadMap Tests', () {
    late List<Subject> testSubjects;
    late SimpleRoadMap roadMap;

    setUp(() {
      // Create test subjects with realistic prerequisite chains
      testSubjects = [
        // Foundation courses (no prerequisites)
        Subject(
          semester_number: 1,
          status: false,
          code: 'MATH101',
          englishName: 'Calculus I',
          arabicName: 'التفاضل والتكامل الأول',
          dependencies: [],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 4,
        ),
        Subject(
          semester_number: 1,
          status: false,
          code: 'CS101',
          englishName: 'Programming Fundamentals',
          arabicName: 'أساسيات البرمجة',
          dependencies: [],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 1,
          status: false,
          code: 'ENG101',
          englishName: 'English I',
          arabicName: 'الإنجليزية الأولى',
          dependencies: [],
          prerequisites: [],
          type: 'General',
          departmentCode: 'ENG',
          hours: 3,
        ),
        
        // Second level courses
        Subject(
          semester_number: 2,
          status: false,
          code: 'MATH102',
          englishName: 'Calculus II',
          arabicName: 'التفاضل والتكامل الثاني',
          dependencies: [],
          prerequisites: ['MATH101'],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 4,
        ),
        Subject(
          semester_number: 2,
          status: false,
          code: 'CS102',
          englishName: 'Data Structures',
          arabicName: 'هياكل البيانات',
          dependencies: [],
          prerequisites: ['CS101'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        
        // Advanced courses
        Subject(
          semester_number: 3,
          status: false,
          code: 'CS201',
          englishName: 'Algorithms',
          arabicName: 'الخوارزميات',
          dependencies: [],
          prerequisites: ['CS102', 'MATH102'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 3,
          status: false,
          code: 'CS202',
          englishName: 'Database Systems',
          arabicName: 'أنظمة قواعد البيانات',
          dependencies: [],
          prerequisites: ['CS102'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        
        // Final courses
        Subject(
          semester_number: 4,
          status: false,
          code: 'CS301',
          englishName: 'Software Engineering',
          arabicName: 'هندسة البرمجيات',
          dependencies: [],
          prerequisites: ['CS201', 'CS202'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
      ];

      roadMap = SimpleRoadMap(
        testSubjects,
        21, // max_semester_hours
        18, // optimal_semester_hours
        12, // min_semester_hours
        40
      );
    });

    test('should initialize correctly', () {
      expect(roadMap.subjects.length, equals(8));
      expect(roadMap.max_semester_hours, equals(18));
      expect(roadMap.optimal_semester_hours, equals(15));
      expect(roadMap.min_semester_hours, equals(12));
      expect(roadMap.required_graduation_hours, equals(20)); // Total hours from test subjects
    });

    test('should generate roadmap for realistic graduation timeline', () {
      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 2; // Graduate in 2 years
      
      List<Semester>? result = roadMap.generateRoadMap(targetYear);
      print('result');
      for (Semester semester in result!) {
        print(semester.subjects);
        print(semester.hours);
        print('================');
      }
      expect(result, isNotNull);
      expect(result!.length, greaterThan(0));
      expect(result.length, lessThanOrEqualTo(4)); // Max 4 semesters in 2 years
      
      // Verify total hours
      int totalHours = result.fold(0, (sum, semester) => sum + semester.hours);
      expect(totalHours, greaterThanOrEqualTo(roadMap.required_graduation_hours));
    });

    test('should respect semester hour limits', () {
      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 2;
      
      List<Semester>? result = roadMap.generateRoadMap(targetYear);
      
      expect(result, isNotNull);
      
      for (Semester semester in result!) {
        expect(semester.hours, lessThanOrEqualTo(roadMap.max_semester_hours));
        if (semester.subjects.isNotEmpty) {
          expect(semester.hours, greaterThanOrEqualTo(roadMap.min_semester_hours));
        }
      }
    });

    test('should respect prerequisite constraints', () {
      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 3; // More time for complex prerequisites
      
      List<Semester>? result = roadMap.generateRoadMap(targetYear);
      
      expect(result, isNotNull);
      expect(roadMap.validateRoadmap(result!), isTrue);
    });

    test('should handle impossible graduation timeline', () {
      int currentYear = DateTime.now().year;
      int targetYear = currentYear; // Try to graduate this year (impossible)
      
      List<Semester>? result = roadMap.generateRoadMap(targetYear);
      
      expect(result, isNull);
    });

    test('should handle insufficient time for all subjects', () {
      // Create scenario with too many hours for available time
      List<Subject> heavySubjects = List.generate(20, (index) => Subject(
        semester_number: 1,
        status: false,
        code: 'HEAVY$index',
        englishName: 'Heavy Subject $index',
        arabicName: 'مادة ثقيلة $index',
        dependencies: [],
        prerequisites: [],
        type: 'Core',
        departmentCode: 'HEAVY',
        hours: 4,
      ));

      SimpleRoadMap heavyRoadMap = SimpleRoadMap(
        heavySubjects,
        18, // max_semester_hours
        15, // optimal_semester_hours
        12, // min_semester_hours
        3
      );

      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 1; // Only 1 year = 2 semesters = max 36 hours
      
      List<Semester>? result = heavyRoadMap.generateRoadMap(targetYear);
      
      expect(result, isNull); // Should be impossible (80 hours in 36 hour capacity)
    });

    test('should validate roadmap correctly', () {
      // Create a valid roadmap manually
      List<Semester> validRoadmap = [
        Semester(number: 1)..addSubject(testSubjects[0])..addSubject(testSubjects[1])..addSubject(testSubjects[2]), // 10 hours
        Semester(number: 2)..addSubject(testSubjects[3])..addSubject(testSubjects[4]), // 7 hours
        Semester(number: 3)..addSubject(testSubjects[5])..addSubject(testSubjects[6]), // 6 hours
        Semester(number: 4)..addSubject(testSubjects[7]), // 3 hours
      ];
      
      expect(roadMap.validateRoadmap(validRoadmap), isTrue);
    });

    test('should detect prerequisite violations in validation', () {
      // Create invalid roadmap with prerequisite violation
      List<Semester> invalidRoadmap = [
        Semester(number: 1)..addSubject(testSubjects[5]), // CS201 without prerequisites
      ];
      
      expect(roadMap.validateRoadmap(invalidRoadmap), isFalse);
    });

    test('should handle empty subject list', () {
      SimpleRoadMap emptyRoadMap = SimpleRoadMap(
        [],
        18, 15, 12, 10
      );

      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 1;
      
      List<Semester>? result = emptyRoadMap.generateRoadMap(targetYear);
      
      expect(result, isNotNull);
      expect(result!.isEmpty, isTrue);
    });

    test('should prioritize subjects by semester number and prerequisites', () {
      int currentYear = DateTime.now().year;
      int targetYear = currentYear + 4; // Plenty of time
      
      List<Semester>? result = roadMap.generateRoadMap(targetYear);
      
      expect(result, isNotNull);
      
      // Verify that foundation courses come before advanced courses
      Set<String> completedCodes = {};
      for (Semester semester in result!) {
        for (Subject subject in semester.subjects) {
          // Check prerequisites are met
          for (String prereq in subject.prerequisites) {
            expect(completedCodes.contains(prereq), isTrue, 
              reason: '${subject.code} scheduled before prerequisite $prereq');
          }
          completedCodes.add(subject.code);
        }
      }
    });
  });
}
