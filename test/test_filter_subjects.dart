import 'package:test/test.dart';
import '../scripts/FilterSubjects.dart';
import '../models/Subject.dart';
import '../helper.dart';

void main() {
  group('FilterSubjects Tests', () {
    late List<Subject> testSubjects;
    late FilterSubjects filterSubjects;

    setUp(() {
      // Create a comprehensive set of test subjects with various prerequisite scenarios
      testSubjects = [
        // Basic subjects with no prerequisites
        Subject(
          semester_number: 1,
          status: true, // Completed
          code: 'MATH101',
          englishName: 'Calculus I',
          arabicName: 'التفاضل والتكامل الأول',
          dependencies: ['MATH102'],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 3,
        ),
        Subject(
          semester_number: 1,
          status: true, // Completed
          code: 'CS100',
          englishName: 'Introduction to Computing',
          arabicName: 'مقدمة في الحاسوب',
          dependencies: ['CS101'],
          prerequisites: [],
          type: 'Core',
          departmentCode: 'CS',
          hours: 2,
        ),
        
        // Subjects with prerequisites (some available, some not)
        Subject(
          semester_number: 2,
          status: false, // Not completed - should be available
          code: 'MATH102',
          englishName: 'Calculus II',
          arabicName: 'التفاضل والتكامل الثاني',
          dependencies: ['PHYS101'],
          prerequisites: ['MATH101'],
          type: 'Core',
          departmentCode: 'MATH',
          hours: 3,
        ),
        Subject(
          semester_number: 2,
          status: false, // Not completed - should be available
          code: 'CS101',
          englishName: 'Programming Fundamentals',
          arabicName: 'أساسيات البرمجة',
          dependencies: ['CS102'],
          prerequisites: ['CS100'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 4,
        ),
        
        // Subjects with unmet prerequisites (should not be available)
        Subject(
          semester_number: 3,
          status: false, // Not completed - should NOT be available (prereq not met)
          code: 'CS102',
          englishName: 'Data Structures',
          arabicName: 'هياكل البيانات',
          dependencies: [],
          prerequisites: ['CS101'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        Subject(
          semester_number: 3,
          status: false, // Not completed - should NOT be available (prereq not met)
          code: 'PHYS101',
          englishName: 'Physics I',
          arabicName: 'الفيزياء الأولى',
          dependencies: [],
          prerequisites: ['MATH102'],
          type: 'Core',
          departmentCode: 'PHYS',
          hours: 3,
        ),
        
        // Subject with multiple prerequisites
        Subject(
          semester_number: 4,
          status: false,
          code: 'CS201',
          englishName: 'Algorithms',
          arabicName: 'الخوارزميات',
          dependencies: [],
          prerequisites: ['CS102', 'MATH102'],
          type: 'Core',
          departmentCode: 'CS',
          hours: 3,
        ),
        
        // Already completed subject
        Subject(
          semester_number: 2,
          status: true, // Completed - should not appear in possible subjects
          code: 'ENG101',
          englishName: 'English I',
          arabicName: 'الإنجليزية الأولى',
          dependencies: [],
          prerequisites: [],
          type: 'General',
          departmentCode: 'ENG',
          hours: 2,
        ),
      ];

      filterSubjects = FilterSubjects(testSubjects);
    });

    group('get_subject helper function', () {
      test('should find existing subject by code', () {
        final subject = get_subject(testSubjects, 'MATH101');
        expect(subject, isNotNull);
        expect(subject!.code, equals('MATH101'));
        expect(subject.englishName, equals('Calculus I'));
      });

      test('should return null for non-existent subject', () {
        final subject = get_subject(testSubjects, 'NONEXISTENT');
        expect(subject, isNull);
      });

      test('should be case-sensitive', () {
        final subject = get_subject(testSubjects, 'math101');
        expect(subject, isNull);
      });

      test('should return first match if duplicates exist', () {
        // Add a duplicate
        final duplicate = Subject(
          semester_number: 5,
          status: false,
          code: 'MATH101',
          englishName: 'Duplicate Calculus',
          arabicName: 'تفاضل مكرر',
          dependencies: [],
          prerequisites: [],
          type: 'Elective',
          departmentCode: 'MATH',
          hours: 3,
        );
        testSubjects.add(duplicate);

        final subject = get_subject(testSubjects, 'MATH101');
        expect(subject, isNotNull);
        expect(subject!.englishName, equals('Calculus I')); // Should be the first one
      });
    });

    group('finished_prerequisites method', () {
      test('should return true for subject with no prerequisites', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'MATH101');
        final result = filterSubjects.testFinishedPrerequisites(subject);
        expect(result, isTrue);
      });

      test('should return true when all prerequisites are completed', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'MATH102');
        final result = filterSubjects.testFinishedPrerequisites(subject);
        expect(result, isTrue); // MATH101 is completed
      });

      test('should return false when prerequisites are not completed', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'CS102');
        final result = filterSubjects.testFinishedPrerequisites(subject);
        expect(result, isFalse); // CS101 is not completed
      });

      test('should return false when some prerequisites are not completed', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'CS201');
        final result = filterSubjects.testFinishedPrerequisites(subject);
        expect(result, isFalse); // CS102 is not completed (even though MATH102 prereq would be met)
      });

      test('should handle non-existent prerequisite gracefully', () {
        // Create a subject with non-existent prerequisite
        final subjectWithBadPrereq = Subject(
          semester_number: 5,
          status: false,
          code: 'TEST999',
          englishName: 'Test Subject',
          arabicName: 'مادة تجريبية',
          dependencies: [],
          prerequisites: ['NONEXISTENT'],
          type: 'Test',
          departmentCode: 'TEST',
          hours: 1,
        );

        final result = filterSubjects.testFinishedPrerequisites(subjectWithBadPrereq);
        expect(result, isTrue); // Should handle gracefully and return true
      });
    });

    group('get_possible_subjects method', () {
      test('should return subjects that are not completed and have met prerequisites', () {
        final possibleSubjects = filterSubjects.get_possible_subjects();
        
        // Should include MATH102 and CS101 (prerequisites met, not completed)
        final codes = possibleSubjects.map((s) => s.code).toList();
        expect(codes, contains('MATH102'));
        expect(codes, contains('CS101'));
        
        // Should NOT include completed subjects
        expect(codes, isNot(contains('MATH101')));
        expect(codes, isNot(contains('CS100')));
        expect(codes, isNot(contains('ENG101')));
        
        // Should NOT include subjects with unmet prerequisites
        expect(codes, isNot(contains('CS102')));
        expect(codes, isNot(contains('PHYS101')));
        expect(codes, isNot(contains('CS201')));
      });

      test('should return empty list when no subjects are available', () {
        // Create a scenario where truly no subjects are available (all completed)
        final noAvailableSubjects = [
          Subject(
            semester_number: 1,
            status: true, // Completed
            code: 'COMP1',
            englishName: 'Completed 1',
            arabicName: 'مكتمل 1',
            dependencies: [],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'TEST',
            hours: 3,
          ),
          Subject(
            semester_number: 2,
            status: true, // Also completed
            code: 'COMP2',
            englishName: 'Completed 2',
            arabicName: 'مكتمل 2',
            dependencies: [],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'TEST',
            hours: 3,
          ),
        ];

        final emptyFilter = FilterSubjects(noAvailableSubjects);
        final possibleSubjects = emptyFilter.get_possible_subjects();
        expect(possibleSubjects, isEmpty);
      });

      test('should maintain original list order after sorting by semester', () {
        final possibleSubjects = filterSubjects.get_possible_subjects();
        
        // Verify subjects are in semester order
        for (int i = 1; i < possibleSubjects.length; i++) {
          expect(
            possibleSubjects[i].semester_number,
            greaterThanOrEqualTo(possibleSubjects[i - 1].semester_number),
          );
        }
      });
    });

    group('Constructor and sorting', () {
      test('should sort subjects by semester number', () {
        // Verify that adjacency_list is sorted by semester_number
        for (int i = 1; i < filterSubjects.adjacency_list.length; i++) {
          expect(
            filterSubjects.adjacency_list[i].semester_number,
            greaterThanOrEqualTo(filterSubjects.adjacency_list[i - 1].semester_number),
          );
        }
      });

      test('should handle empty subject list', () {
        final emptyFilter = FilterSubjects([]);
        expect(emptyFilter.adjacency_list, isEmpty);
        expect(emptyFilter.get_possible_subjects(), isEmpty);
      });
    });

    group('traverse_single_subjects_tree method', () {
      test('should return true for incomplete subject with met prerequisites', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'MATH102');
        final result = filterSubjects.traverse_single_subjects_tree(subject);
        expect(result, isTrue);
      });

      test('should return false for incomplete subject with unmet prerequisites', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'CS102');
        final result = filterSubjects.traverse_single_subjects_tree(subject);
        expect(result, isFalse);
      });

      test('should handle completed subjects', () {
        final subject = testSubjects.firstWhere((s) => s.code == 'MATH101');
        final result = filterSubjects.traverse_single_subjects_tree(subject);
        expect(result, isFalse); // Method returns false for completed subjects
      });

      test('should throw exception for invalid prerequisite code', () {
        final subjectWithBadPrereq = Subject(
          semester_number: 5,
          status: true, // Completed, so it will traverse prerequisites
          code: 'BAD_SUBJECT',
          englishName: 'Bad Subject',
          arabicName: 'مادة سيئة',
          dependencies: [],
          prerequisites: ['INVALID_CODE'],
          type: 'Test',
          departmentCode: 'TEST',
          hours: 1,
        );
        
        expect(
          () => filterSubjects.traverse_single_subjects_tree(subjectWithBadPrereq),
          throwsException,
        );
      });
    });

    group('get_possible_subjects_v2 method', () {
      test('should find subjects with no prerequisites that can be taken', () {
        final possibleSubjects = filterSubjects.get_possible_subjects_v2();
        
        // This method looks for subjects with no prerequisites first
        // Then checks if they can be taken using traverse_single_subjects_tree
        expect(possibleSubjects, isNotNull);
        expect(possibleSubjects, isList);
        
        // All returned subjects should have no prerequisites
        for (final subject in possibleSubjects) {
          expect(subject.prerequisites, isEmpty);
        }
      });

      test('should handle subjects with no prerequisites correctly', () {
        // Create a simple test case with only no-prerequisite subjects
        final simpleSubjects = [
          Subject(
            semester_number: 1,
            status: false,
            code: 'SIMPLE1',
            englishName: 'Simple 1',
            arabicName: 'بسيط 1',
            dependencies: [],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'TEST',
            hours: 3,
          ),
          Subject(
            semester_number: 1,
            status: true,
            code: 'SIMPLE2',
            englishName: 'Simple 2',
            arabicName: 'بسيط 2',
            dependencies: [],
            prerequisites: [],
            type: 'Core',
            departmentCode: 'TEST',
            hours: 3,
          ),
        ];
        
        final simpleFilter = FilterSubjects(simpleSubjects);
        final possibleSubjects = simpleFilter.get_possible_subjects_v2();
        
        // Should include SIMPLE1 (not completed, no prerequisites)
        final codes = possibleSubjects.map((s) => s.code).toList();
        expect(codes, contains('SIMPLE1'));
        expect(codes, isNot(contains('SIMPLE2'))); // Completed, so not available
      });
    });

    group('Integration tests', () {
      test('should handle complex prerequisite chains', () {
        // Test a realistic scenario with multiple prerequisite levels
        final possibleSubjects = filterSubjects.get_possible_subjects();
        
        // Verify the logic: 
        // - MATH101, CS100, ENG101 are completed
        // - MATH102 can be taken (MATH101 completed)
        // - CS101 can be taken (CS100 completed)
        // - CS102 cannot be taken (CS101 not completed)
        // - PHYS101 cannot be taken (MATH102 not completed)
        // - CS201 cannot be taken (CS102 not completed)
        
        final codes = possibleSubjects.map((s) => s.code).toSet();
        expect(codes, containsAll(['MATH102', 'CS101']));
        expect(codes.length, equals(2));
      });

      test('should work with real graduation scenario', () {
        // Simulate a student's progress through semesters
        final student = FilterSubjects(testSubjects);
        
        // Get initial possible subjects
        var possible = student.get_possible_subjects();
        expect(possible.length, greaterThan(0));
        
        // Simulate completing a subject
        final mathSubject = student.adjacency_list.firstWhere((s) => s.code == 'MATH102');
        mathSubject.status = true;
        
        // Create new filter with updated data
        final updatedStudent = FilterSubjects(student.adjacency_list);
        var newPossible = updatedStudent.get_possible_subjects();
        
        // Should now include PHYS101 (MATH102 completed)
        final newCodes = newPossible.map((s) => s.code).toList();
        expect(newCodes, contains('PHYS101'));
      });
    });
  });
}
