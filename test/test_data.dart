import '../models/Subject.dart';

final List<Subject> testSubjects = [
  // From test 1: returns subjects with no prerequisites
  Subject(
    semester_number: 1,
    status: true,
    code: 'S1',
    englishName: 'Subject 1',
    arabicName: 'المادة 1',
    dependencies: [],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),
  Subject(
    semester_number: 2,
    status: true,
    code: 'S2',
    englishName: 'Subject 2',
    arabicName: 'المادة 2',
    dependencies: ['S3'],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),
  Subject(
    semester_number: 2,
    status: false,
    code: 'S3',
    englishName: 'Subject 3',
    arabicName: 'المادة 3',
    dependencies: [],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),

  // From test 2: returns subjects with finished prerequisites
  Subject(
    semester_number: 1,
    status: false,
    code: 'S1-finished',
    englishName: 'Subject 1',
    arabicName: 'المادة 1',
    dependencies: [],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),
  Subject(
    semester_number: 2,
    status: true,
    code: 'S2-prereq-finished',
    englishName: 'Subject 2',
    arabicName: 'المادة 2',
    dependencies: ['S1-finished'],
    prerequisites: ['S1-finished'],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),

  // From test 4: circular dependencies
  Subject(
    semester_number: 1,
    status: true,
    code: 'S1-circular',
    englishName: 'Subject 1',
    arabicName: 'المادة 1',
    dependencies: ['S2-circular'],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),
  Subject(
    semester_number: 2,
    status: true,
    code: 'S2-circular',
    englishName: 'Subject 2',
    arabicName: 'المادة 2',
    dependencies: ['S1-circular'],
    prerequisites: [],
    type: 'type',
    departmentCode: 'dept',
    hours: 3,
  ),
];
