import 'package:test/test.dart';

// Import all test files
import 'test_filter_subjects.dart' as filter_tests;
import 'test_helper.dart' as helper_tests;
import 'test_io.dart' as io_tests;
import 'test.dart' as original_tests;

void main() {
  group('🎓 Graduation Algorithm System - Complete Test Suite', () {
    print('\n🚀 Running Complete Test Suite for Graduation Algorithm System');
    print('=' * 70);
    
    group('📚 Original Core Tests', () {
      original_tests.main();
    });

    group('🛠️ Helper Functions Tests', () {
      helper_tests.main();
    });
    
    group('💾 IO Operations Tests', () {
      io_tests.main();
    });
    
    group('🎯 FilterSubjects Class Tests', () {
      filter_tests.main();
    });
  });
}
