import 'package:test/test.dart';
import '../models/Subject.dart';
import '../functions/filtering_stage.dart'; 
import 'test_data.dart'; 

void main() {
  group('get_subject_by_code', () {
    test('returns correct subject when code exists', () {
      final subject = get_subject_by_code('S1');
      expect(subject, isNotNull);
      expect(subject!.code, equals('S1'));
      expect(subject.englishName, equals('Subject 1'));
    });

    test('returns null when code does not exist', () {
      final subject = get_subject_by_code('NON_EXISTENT');
      expect(subject, isNull);
    });

    test('returns first match if duplicates exist', () {
      // Add a duplicate manually
      final duplicate = Subject(
        semester_number: 3,
        status: true,
        code: 'S1',
        englishName: 'Duplicate Subject 1',
        arabicName: 'المادة 1 مكررة',
        dependencies: [],
        prerequisites: [],
        type: 'type',
        departmentCode: 'dept',
        hours: 3,
      );
      testSubjects.add(duplicate);

      final subject = get_subject_by_code('S1');
      expect(subject, isNotNull);
      expect(subject!.englishName, equals('Subject 1'));

      testSubjects.remove(duplicate); // cleanup
    });

    test('is case-sensitive when comparing codes', () {
      final subject = get_subject_by_code('s1'); // lowercase
      expect(subject, isNull);
    });
  });
}
