import './models/Subject.dart';
import './models/Department.dart';

// helper function
void print_subjectArr (List<Subject> subjects) {
    if(subjects.length == 0){
      print("no subjects");
      return;
    }
    for (var subject in subjects) {
        print(subject);
    }
    print('------------------------------------------\n');
}
void print_departmentArr (List<Department> department) {
  if(department.length == 0){
      print("no departments");
      return;
    }
    for (var department in department) {
        print(department);
    }
    print('------------------------------------------\n');
}
