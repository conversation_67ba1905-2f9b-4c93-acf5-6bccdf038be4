import '../test/test_data.dart';
import 'package:collection/collection.dart';
import '../models/Subject.dart';

// Subject? get_subject_by_code(String code) {
//   return null;
// }


Subject? get_subject_by_code(String code) {
  return testSubjects.firstWhereOrNull(
    (subject) => subject.code == code,
  );
}




bool finished_prerequisites(Subject subject) {
  for (var i = 0; i < subject.prerequisites.length; i++) {
    Subject? prerequisites_subject = get_subject_by_code(subject.prerequisites[i]);
    if(prerequisites_subject == null) {
      // to-do throw an error subject_code is not correct
      // throw Exception('Error: Subject code "${subject.prerequisites[i]}" is not correct');
    }
    if (prerequisites_subject != null && !prerequisites_subject.status) {
      return false;
    } 
  }
  return true;
}


// List<Subject> get_possible_subjects(List<Subject> subjects) {
//   List<Subject> possible_subjects = [];
//   for (int i = 0; i < subjects.length; i++) { 
//    if(subjects[i].status == 'finished') {
//       for(String subject_code in subjects[i].dependencies) {
//         Subject? new_subject = get_subject_by_code(subject_code);
//         if (new_subject != null) {
//           subjects.add(new_subject);
//         }
//       }
//       continue;
//     } 
//    if (finished_prerequisites(subjects[i])) {
//      possible_subjects.add(subjects[i]);
//      continue;
//    }
//  }  
//  return possible_subjects;
// }


  List<Subject> get_possible_subjects(List<Subject> all_subjects) {
    List<Subject> possible_subjects = [];
    for(int i = 0; i < all_subjects.length; i++){
      if(!all_subjects[i].status && finished_prerequisites(all_subjects[i])){
        possible_subjects.add(all_subjects[i]);
      }
    }
    return possible_subjects;
  }



  /// Returns a list of possible subjects that can be taken
  /// based on the initial list of subjects provided.
  /// 
  /// The function processes each subject, checking if it is
  /// finished or if prerequisites are met, and dynamically
  /// expands to include dependencies.
  ///
  /// - Parameters:
  ///   - initialSubjects: A list of initial subjects to process.
  /// - Returns: A list of subjects that can be potentially taken.
  // List<Subject> get_possible_subjects(List<Subject> initialSubjects) {
  //   final List<Subject> possibleSubjects = []; 
  //   final Set<String> visitedCodes = {}; 
  //   final List<Subject> queue = List.from(initialSubjects); 

  //   while (queue.isNotEmpty) {
  //     final Subject current = queue.removeAt(0); 

  //     if (visitedCodes.contains(current.code)) {
  //       continue; 
  //     }

  //     visitedCodes.add(current.code); 

  //     if (current.status == true) {
  //       for (String depCode in current.dependencies) {
  //         Subject? newSubject = get_subject_by_code(depCode);
  //         if (newSubject != null && !visitedCodes.contains(newSubject.code)) {
  //           queue.add(newSubject); 
  //         }
  //       }
  //       continue; 
  //     }

  //     if (finished_prerequisites(current)) {
  //       possibleSubjects.add(current); 
  //     }
  //   }

  //   return possibleSubjects; 
  // }



