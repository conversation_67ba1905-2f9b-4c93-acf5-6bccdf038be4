import '../test/test_data.dart';
import 'package:collection/collection.dart';
import '../models/Subject.dart';


Subject? get_subject_by_code(String code) {
  return testSubjects.firstWhereOrNull(
    (subject) => subject.code == code,
  );
}




bool finished_prerequisites(Subject subject) {
  for (var i = 0; i < subject.prerequisites.length; i++) {
    Subject? prerequisites_subject = get_subject_by_code(subject.prerequisites[i]);
    if(prerequisites_subject == null) {
      // to-do throw an error subject_code is not correct
      // throw Exception('Error: Subject code "${subject.prerequisites[i]}" is not correct');
    }
    if (prerequisites_subject != null && !prerequisites_subject.status) {
      return false;
    } 
  }
  return true;
}


  List<Subject> get_possible_subjects(List<Subject> all_subjects) {
    List<Subject> possible_subjects = [];
    for(int i = 0; i < all_subjects.length; i++){
      if(!all_subjects[i].status && finished_prerequisites(all_subjects[i])){
        possible_subjects.add(all_subjects[i]);
      }
    }
    return possible_subjects;
  }
