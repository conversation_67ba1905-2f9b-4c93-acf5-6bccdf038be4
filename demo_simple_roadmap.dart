import 'models/SimpleRoadMap.dart';
import 'models/Subject.dart';
import 'models/Semesetr.dart';

void main() {
  print('🎓 SimpleRoadMap Algorithm Demo');
  print('=' * 50);
  
  // Create sample subjects for a Computer Science program
  List<Subject> subjects = [
    // First Year - Foundation Courses
    Subject(
      semester_number: 1,
      status: false,
      code: 'MATH101',
      englishName: 'Calculus I',
      arabicName: 'التفاضل والتكامل الأول',
      dependencies: [],
      prerequisites: [],
      type: 'Core',
      departmentCode: 'MATH',
      hours: 4,
    ),
    Subject(
      semester_number: 1,
      status: false,
      code: 'CS101',
      englishName: 'Programming Fundamentals',
      arabicName: 'أساسيات البرمجة',
      dependencies: [],
      prerequisites: [],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 1,
      status: false,
      code: 'ENG101',
      englishName: 'English I',
      arabicName: 'الإنجليزية الأولى',
      dependencies: [],
      prerequisites: [],
      type: 'General',
      departmentCode: 'ENG',
      hours: 3,
    ),
    Subject(
      semester_number: 1,
      status: false,
      code: 'PHYS101',
      englishName: 'Physics I',
      arabicName: 'الفيزياء الأولى',
      dependencies: [],
      prerequisites: [],
      type: 'Core',
      departmentCode: 'PHYS',
      hours: 4,
    ),
    
    // Second Year Courses
    Subject(
      semester_number: 2,
      status: false,
      code: 'MATH102',
      englishName: 'Calculus II',
      arabicName: 'التفاضل والتكامل الثاني',
      dependencies: [],
      prerequisites: ['MATH101'],
      type: 'Core',
      departmentCode: 'MATH',
      hours: 4,
    ),
    Subject(
      semester_number: 2,
      status: false,
      code: 'CS102',
      englishName: 'Data Structures',
      arabicName: 'هياكل البيانات',
      dependencies: [],
      prerequisites: ['CS101'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 2,
      status: false,
      code: 'ENG102',
      englishName: 'English II',
      arabicName: 'الإنجليزية الثانية',
      dependencies: [],
      prerequisites: ['ENG101'],
      type: 'General',
      departmentCode: 'ENG',
      hours: 3,
    ),
    
    // Third Year Courses
    Subject(
      semester_number: 3,
      status: false,
      code: 'CS201',
      englishName: 'Algorithms',
      arabicName: 'الخوارزميات',
      dependencies: [],
      prerequisites: ['CS102', 'MATH102'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 3,
      status: false,
      code: 'CS202',
      englishName: 'Database Systems',
      arabicName: 'أنظمة قواعد البيانات',
      dependencies: [],
      prerequisites: ['CS102'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 3,
      status: false,
      code: 'CS203',
      englishName: 'Computer Networks',
      arabicName: 'شبكات الحاسوب',
      dependencies: [],
      prerequisites: ['CS102'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    
    // Fourth Year Courses
    Subject(
      semester_number: 4,
      status: false,
      code: 'CS301',
      englishName: 'Software Engineering',
      arabicName: 'هندسة البرمجيات',
      dependencies: [],
      prerequisites: ['CS201', 'CS202'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    Subject(
      semester_number: 4,
      status: false,
      code: 'CS302',
      englishName: 'Operating Systems',
      arabicName: 'أنظمة التشغيل',
      dependencies: [],
      prerequisites: ['CS201', 'CS203'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 3,
    ),
    
    // Final Project
    Subject(
      semester_number: 4,
      status: false,
      code: 'CS401',
      englishName: 'Graduation Project',
      arabicName: 'مشروع التخرج',
      dependencies: [],
      prerequisites: ['CS301', 'CS302'],
      type: 'Core',
      departmentCode: 'CS',
      hours: 6,
    ),
  ];

  // Create SimpleRoadMap instance
  SimpleRoadMap roadMap = SimpleRoadMap(
    subjects,
    18, // max_semester_hours
    15, // optimal_semester_hours
    12, // min_semester_hours
  );

  print('\n📊 Program Information:');
  print('Total Subjects: ${subjects.length}');
  print('Total Required Hours: ${roadMap.required_graduation_hours}');
  print('Current Year: ${roadMap.current_year}');
  print('Max Hours per Semester: ${roadMap.max_semester_hours}');
  print('Optimal Hours per Semester: ${roadMap.optimal_semester_hours}');
  print('Min Hours per Semester: ${roadMap.min_semester_hours}');

  // Demo 1: Generate roadmap for 3 years
  print('\n🎯 Demo 1: Generate roadmap for graduation in 3 years');
  print('-' * 50);
  int targetYear1 = DateTime.now().year + 3;
  List<Semester>? roadmap1 = roadMap.generateRoadMap(targetYear1);
  
  if (roadmap1 != null) {
    print('✅ Successfully generated roadmap for $targetYear1');
    print('Number of semesters needed: ${roadmap1.length}');
  } else {
    print('❌ Could not generate roadmap for $targetYear1');
  }

  // Demo 2: Try impossible timeline (1 year)
  print('\n🎯 Demo 2: Try impossible timeline (1 year)');
  print('-' * 50);
  int targetYear2 = DateTime.now().year + 1;
  List<Semester>? roadmap2 = roadMap.generateRoadMap(targetYear2);
  
  if (roadmap2 != null) {
    print('✅ Successfully generated roadmap for $targetYear2');
  } else {
    print('❌ Could not generate roadmap for $targetYear2 (as expected)');
  }

  // Demo 3: Generate roadmap with plenty of time (5 years)
  print('\n🎯 Demo 3: Generate roadmap with plenty of time (5 years)');
  print('-' * 50);
  int targetYear3 = DateTime.now().year + 5;
  List<Semester>? roadmap3 = roadMap.generateRoadMap(targetYear3);
  
  if (roadmap3 != null) {
    print('✅ Successfully generated roadmap for $targetYear3');
    print('Number of semesters needed: ${roadmap3.length}');
    
    // Validate the roadmap
    print('\n🔍 Validating roadmap...');
    bool isValid = roadMap.validateRoadmap(roadmap3);
    if (isValid) {
      print('✅ Roadmap validation passed!');
    } else {
      print('❌ Roadmap validation failed!');
    }
  } else {
    print('❌ Could not generate roadmap for $targetYear3');
  }

  print('\n🎉 Demo completed!');
  print('=' * 50);
}
