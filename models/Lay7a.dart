import './Subject.dart';
import './Department.dart';

class Lay7a {
  final List<Subject> subjects;
  final String collegeType;
  final Department department;
  final int year;
  Lay7a({
    required this.year,
    required this.subjects,
    required this.collegeType,
    required this.department,
  });
  @override
  String toString() {
    return 'Lay7a(\n'
    ' year: $year,\n'
    ' collegeType: $collegeType,\n'
    ' department: $department,\n'
    ' subjects: $subjects\n'
    ')\n';
  }
}