// add 

class Department {
  final String code;
  final String arabicName;
  final String englishName;
  final String departmentType;
  final String collegeType;
  final int total_hours;
  Department({
    required this.total_hours,
    required this.code,
    required this.arabicName,
    required this.englishName,
    required this.departmentType,
    required this.collegeType,
  });
  @override
  String toString() {
    return 'Department('
    ' code: $code,\n'
    ' englishName: $englishName,\n'
    ' arabicName: $arabicName\n'
    ' total_hours $total_hours\n'
    ')\n';
  }
}
