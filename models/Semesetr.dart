import './Subject.dart';

class Semester {
  List<Subject> _subjects = [];
  int _number;
  int _semester_hours_sum  = 0;
  // Constructor
  Semester({required int number}) : _number = number;
  // Clear the subjects
  void clear() {
    _subjects = [];
  }
  // setter for the _semester_hours_sum
  set semester_hours_sum (int hours_sum) {
    _semester_hours_sum = hours_sum;
  }
  // Getter for number
  int get number => _number;
  // Getter for hours
  int get hours => _semester_hours_sum;
  // Getter for hours
  List<Subject> get subjects => _subjects;
  // Increment the number
  void incrementNumber() {
    _number++;
  }

  // Static clone method
  static Semester clone(Semester semester) {
    Semester copySemester = Semester(number: semester._number);
    copySemester._subjects = List.from(semester._subjects);
    copySemester.semester_hours_sum = semester.hours;
    return copySemester;
  }

  // Add a subject
  void addSubject(Subject subject) {
    this._semester_hours_sum += subject.hours;
    _subjects.add(subject);
  }
}