class Subject {
  final String code;
  final String englishName;
  final String arabicName;
  final List<String> dependencies;
  final List<String> prerequisites;
  final String type;
  final String departmentCode;
  final int hours;
  bool status;
  final int semester_number;
  Subject({
    required this.semester_number,
    required this.status,
    required this.code,
    required this.englishName,
    required this.arabicName,
    required this.dependencies,
    required this.prerequisites,
    required this.type,
    required this.departmentCode,
    required this.hours,
  });

  // Deep copy constructor
  Subject clone() {
    return Subject(
      semester_number: this.semester_number,
      status: this.status,
      code: this.code,
      englishName: this.englishName,
      arabicName: this.arabicName,
      dependencies: List<String>.from(dependencies),
      prerequisites: List<String>.from(prerequisites),
      type: this.type,
      departmentCode: this.departmentCode,
      hours: this.hours,
    );
  }

  // Convert Subject to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'englishName': englishName,
      'hours': hours,
      'prerequisites': prerequisites,
      'arabicName': arabicName,
      'dependencies': dependencies,
      'type': type,
      'departmentCode': departmentCode,
      'semester_number': semester_number,
      'status': status
    };
  }

  // Create a Subject from JSON
   factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      code: json['code'],
      englishName: json['englishName'],
      hours: json['hours'],
      prerequisites: List<String>.from(json['prerequisites']),
      arabicName: json['arabicName'],
      dependencies: List<String>.from(json['dependencies']),
      type: json['type'],
      departmentCode: json['departmentCode'],
      status: json['status'],
      semester_number: json['semester_number']
    );
  }

   @override
  String toString() {
    return 'Subject(\n'
    ' code: $code,\n'
    ' englishName: $englishName,\n'
    ' arabicName: $arabicName,\n'
    ' dependencies: $dependencies,\n'
    ' prerequisites: $prerequisites, \n'
    ' type: $type,\n'
    ' departmentCode: $departmentCode,\n'
    ' hours: $hours\n'
    ' semester_number: $semester_number\n'
    ' status: $status\n'
    ')\n';
  }
}
