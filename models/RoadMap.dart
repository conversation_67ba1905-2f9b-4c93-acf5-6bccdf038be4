import 'dart:convert';
import 'dart:math';
import './Subject.dart';
import './Semesetr.dart';
import '../functions/filtering_stage.dart';

class RoadmapGenerator {
  List<Subject> subjects;
  int current_year = DateTime.now().year;
  int? graduation_year;
  int max_semester_hours;
  int optimal_semester_hours;
  int min_semester_hours;
  
  RoadmapGenerator({
    required this.subjects,
    required this.graduation_year,
    required this.max_semester_hours,
    required this.optimal_semester_hours,
    required this.min_semester_hours,
  });

  List<Semester> generateRoadmap() {
    if (graduation_year == null) {
      throw Exception('Graduation year must be set');
    }
    
    // Calculate total semesters available (2 semesters per year)
    int total_semesters = (graduation_year! - current_year) * 2;
    
    if (total_semesters <= 0) {
      throw Exception('Graduation year must be in the future');
    }
    
    // Create deep copy of subjects to avoid modifying original
    List<Subject> working_subjects = subjects.map((s) => s.clone()).toList();
    
    // Initialize semesters
    List<Semester> semesters = [];
    for (int i = 1; i <= total_semesters; i++) {
      semesters.add(Semester(number: i));
    }
    
    // Try to generate roadmap using backtracking
    if (_generateRoadmapRecursive(working_subjects, semesters, 0)) {
      return semesters;
    } else {
      throw Exception('Unable to generate roadmap with given constraints');
    }
  }
  
  bool _generateRoadmapRecursive(List<Subject> working_subjects, List<Semester> semesters, int current_semester_index) {
    // Base case: if we've filled all semesters
    if (current_semester_index >= semesters.length) {
      // Check if all subjects are completed
      return _allSubjectsCompleted(working_subjects);
    }
    
    // Get current semester
    Semester current_semester = semesters[current_semester_index];
    
    // Try different combinations of subjects for this semester
    return _fillSemester(working_subjects, current_semester, semesters, current_semester_index);
  }
  
  bool _fillSemester(List<Subject> working_subjects, Semester current_semester, List<Semester> semesters, int current_semester_index) {
    // Get possible subjects for this semester
    List<Subject> possible_subjects = get_possible_subjects(working_subjects);
    
    // If no possible subjects and still have incomplete subjects, try next semester
    if (possible_subjects.isEmpty) {
      return _generateRoadmapRecursive(working_subjects, semesters, current_semester_index + 1);
    }
    
    // Try to fill semester with optimal hours first
    if (_tryFillSemesterWithHours(working_subjects, current_semester, semesters, current_semester_index, possible_subjects, optimal_semester_hours)) {
      return true;
    }
    
    // If optimal doesn't work, try with any valid combination within min-max range
    return _tryFillSemesterWithRange(working_subjects, current_semester, semesters, current_semester_index, possible_subjects);
  }
  
  bool _tryFillSemesterWithHours(List<Subject> working_subjects, Semester current_semester, List<Semester> semesters, int current_semester_index, List<Subject> possible_subjects, int target_hours) {
    // Try to find combination that matches target hours
    List<Subject> combination = _findCombination(possible_subjects, target_hours);
    
    if (combination.isNotEmpty) {
      return _testCombination(working_subjects, current_semester, semesters, current_semester_index, combination);
    }
    
    return false;
  }
  
  bool _tryFillSemesterWithRange(List<Subject> working_subjects, Semester current_semester, List<Semester> semesters, int current_semester_index, List<Subject> possible_subjects) {
    // Try different combinations within the hour range
    for (int target_hours = max_semester_hours; target_hours >= min_semester_hours; target_hours--) {
      List<Subject> combination = _findCombination(possible_subjects, target_hours);
      
      if (combination.isNotEmpty) {
        if (_testCombination(working_subjects, current_semester, semesters, current_semester_index, combination)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  bool _testCombination(List<Subject> working_subjects, Semester current_semester, List<Semester> semesters, int current_semester_index, List<Subject> combination) {
    // Save current state
    List<Subject> backup_subjects = working_subjects.map((s) => s.clone()).toList();
    Semester backup_semester = Semester.clone(current_semester);
    
    // Add subjects to current semester and mark as completed
    for (Subject subject in combination) {
      current_semester.addSubject(subject);
      // Find and mark subject as completed in working_subjects
      Subject? working_subject = working_subjects.firstWhere((s) => s.code == subject.code);
      if (working_subject != null) {
        working_subject.status = true;
      }
    }
    
    // Recursively try to fill remaining semesters
    if (_generateRoadmapRecursive(working_subjects, semesters, current_semester_index + 1)) {
      return true;
    }
    
    // Backtrack: restore state
    working_subjects.clear();
    working_subjects.addAll(backup_subjects);
    current_semester.clear();
    current_semester.semester_hours_sum = backup_semester.hours;
    for (Subject subject in backup_semester.subjects) {
      current_semester.addSubject(subject);
    }
    
    return false;
  }
  
  List<Subject> _findCombination(List<Subject> possible_subjects, int target_hours) {
    List<Subject> best_combination = [];
    int best_hours = 0;
    
    // Try to find combination closest to target hours without exceeding max
    _findCombinationRecursive(possible_subjects, [], 0, 0, target_hours, best_combination, best_hours);
    
    return best_combination;
  }
  
  void _findCombinationRecursive(List<Subject> possible_subjects, List<Subject> current_combination, int current_hours, int start_index, int target_hours, List<Subject> best_combination, int best_hours) {
    // If current combination is better than best, update best
    if (current_hours >= min_semester_hours && current_hours <= max_semester_hours) {
      if (current_hours > best_hours && current_hours <= target_hours) {
        best_combination.clear();
        best_combination.addAll(current_combination);
        best_hours = current_hours;
      }
    }
    
    // Try adding more subjects
    for (int i = start_index; i < possible_subjects.length; i++) {
      Subject subject = possible_subjects[i];
      int new_hours = current_hours + subject.hours;
      
      // Only proceed if within max hours
      if (new_hours <= max_semester_hours) {
        current_combination.add(subject);
        _findCombinationRecursive(possible_subjects, current_combination, new_hours, i + 1, target_hours, best_combination, best_hours);
        current_combination.removeLast();
      }
    }
  }
  
  bool _allSubjectsCompleted(List<Subject> working_subjects) {
    return working_subjects.every((subject) => subject.status);
  }
  
  // Helper method to print roadmap
  void printRoadmap(List<Semester> semesters) {
    print('=== ACADEMIC ROADMAP ===');
    print('Current Year: $current_year');
    print('Graduation Year: $graduation_year');
    print('');
    
    for (Semester semester in semesters) {
      if (semester.subjects.isNotEmpty) {
        int year = current_year + ((semester.number - 1) ~/ 2);
        String term = (semester.number % 2 == 1) ? 'Fall' : 'Spring';
        
        print('Semester ${semester.number} ($term $year) - ${semester.hours} hours:');
        for (Subject subject in semester.subjects) {
          print('  • ${subject.code} - ${subject.englishName} (${subject.hours} hours)');
        }
        print('');
      }
    }
  }
  
  // Helper method to validate roadmap
  bool validateRoadmap(List<Semester> semesters) {
    List<String> completed_subjects = [];
    
    for (Semester semester in semesters) {
      // Check semester hours
      if (semester.subjects.isNotEmpty) {
        if (semester.hours < min_semester_hours || semester.hours > max_semester_hours) {
          print('Error: Semester ${semester.number} has ${semester.hours} hours (should be between $min_semester_hours and $max_semester_hours)');
          return false;
        }
      }
      
      // Check prerequisites
      for (Subject subject in semester.subjects) {
        for (String prerequisite in subject.prerequisites) {
          if (!completed_subjects.contains(prerequisite)) {
            print('Error: Subject ${subject.code} requires prerequisite $prerequisite which is not completed');
            return false;
          }
        }
        completed_subjects.add(subject.code);
      }
    }
    
    return true;
  }
}

// Usage example:
/*
void main() {
  try {
    RoadmapGenerator generator = RoadmapGenerator(
      subjects: testSubjects, // Your subjects list
      graduation_year: 2028,
      max_semester_hours: 18,
      optimal_semester_hours: 15,
      min_semester_hours: 12,
    );
    
    List<Semester> roadmap = generator.generateRoadmap();
    generator.printRoadmap(roadmap);
    
    if (generator.validateRoadmap(roadmap)) {
      print('Roadmap is valid!');
    } else {
      print('Roadmap has validation errors.');
    }
  } catch (e) {
    print('Error generating roadmap: $e');
  }
}
*/