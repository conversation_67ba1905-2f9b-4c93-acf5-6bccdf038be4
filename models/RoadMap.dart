import 'Subject.dart';
import 'dart:ffi';
import '../io.dart';
import 'Department.dart';
import 'Lay7a.dart';
import 'Semesetr.dart';
import '../scripts/FilterSubjects.dart';
import '../Exceptions.dart';
import '../functions/filtering_stage.dart';

void print_msg(var msg){
  print('==================================================');
  print(msg);
  print('==================================================');
}


class RoadMap {
  List<Subject> subjects;
  int current_year = DateTime.now().year;
  int? graduation_year = null;
  String? semester_hours_json =  null;
  int max_semester_hours = 12;
  int optimal_semester_hours = 12;
  int min_semester_hours = 12;
  
  RoadMap(this.subjects, this.max_semester_hours, this.optimal_semester_hours, this.min_semester_hours);
/*
  int get_roadMap_settings (int current_semester_number, int hours_left, int total_gpa, int last_semester_gpa) {
    int max_hours = get_max_semester_hours(last_semester_gpa, total_gpa);
    int optimal_hours = get_optimal_semester_hours(last_semester_gpa, total_gpa);
    int summer_max_hours =  9;
    if(hours_left <= optimal_hours * (10-current_semester_number+1)){
    }else if(hours_left <= max_hours*(10-current_semester_number+1)){

    }else{
      int summers_hours_count = hours_left - max_hours*(10-current_semester_number+1);
    }
    throw UnimplementedError();
  }

  int get_optimal_semester_hours (int last_semester_gpa, int total_gpa) {
    if(last_semester_gpa < 2){
      return 12;
    }else if(total_gpa < 2){
      return 14;
    } else return 18;
  }

  int get_max_semester_hours (int last_semester_gpa, int total_gpa) {
    if(last_semester_gpa < 2){
      return 12;
    }else if(total_gpa < 2){
      return 14;
    } else if(total_gpa >= 2 && total_gpa < 3) {
      return 18;
    }else return 21;
  }

  int get_summer_hours(){
    throw UnimplementedError(); 
  }

  bool check_constraint() {
    throw UnimplementedError();
  }
*/

  Semester? dfs (List<Subject> subjects, int current_index, Semester semester, int max_hours) {
    if(current_index >= subjects.length || max_hours < 0) return null;
    if(max_hours == 0) return semester;
  
    //print(semester.hours);
    // try taking the subject 
    Semester new_semester = Semester.clone(semester);
    new_semester.addSubject(subjects[current_index]);
    Semester? res = dfs(subjects, current_index + 1, new_semester, max_hours-subjects[current_index].hours);
    // incase the dfs first branch didnt work
    // try the second branch
    if(res == null){
      //print('second branch');
      // try not taking the subject
      Semester new_semester_2 = Semester.clone(semester);
      res = dfs(subjects, current_index + 1, new_semester_2, max_hours);
      if(res == null){
       // do somethins 
      }
      return res;
    }
    return res;
  }

  Semester fill_semester(List<Subject> subjects, int current_semester_number, int semester_hours_to_fill){
    print('fill_semester(subjects: ${subjects.length}, current_semester_number: ${current_semester_number})');
    Semester semester = new Semester(number: current_semester_number);
    Semester? res = dfs(subjects, 0, semester, optimal_semester_hours);
    if(res == null) {
      throw new Exception('dfs retuned null');
    }else {
      return res;
    }
  }

  // take enrolling year as input say 2019
  // from current date  ----> 
  // from graduation year say 2025 --->  2025 - 2019
  //get remainig semesters

   List<Semester> get_roadMap2 (List<Subject> total_subjects, List<Semester> res_semesters, int remainig_hours, int remaining_semesters_number, int hours_sum, int current_semester_number) {
    if(remaining_semesters_number == 0 && hours_sum >= remainig_hours){
      return res_semesters.map((semester) => Semester.clone(semester)).toList();
    }

    /* // reached max graduation year and still didn't finish required hours
    // take more hours
    if(remaining_semesters_number == 0){
      throw new UnreachedGraduationHoursException('');
    }
    // reached or exceget_roadMap2eded the remaining hours and still didn't reach the graduation year
    // take less hours 
    if(remaining_semesters_number > 0 && hours_sum >= remainig_hours){
      throw ExceddedHoursSumException('');
    } */

    FilterSubjects filter_subjects_class = new FilterSubjects(total_subjects);
    List<Subject> filtered_subjects = filter_subjects_class.get_possible_subjects();
    print(filtered_subjects.length);
    // mark finished subjects in the total_subjects
    total_subjects.forEach((single_subject){
        for (int i=0; i < filtered_subjects.length; i++){
          if(filtered_subjects[i].code == single_subject.code){
            single_subject.status = true;
            break;
          }
        }
    });

    Semester new_semester = fill_semester(total_subjects, current_semester_number, optimal_semester_hours);
    print(new_semester.number);
    print('---');
    List<Semester> cloned_semesters = res_semesters.map((semester) => Semester.clone(semester)).toList();
    cloned_semesters.add(new_semester);

    try {
      return get_roadMap2(total_subjects, cloned_semesters, remainig_hours, remaining_semesters_number-1, hours_sum+new_semester.hours, current_semester_number+1);
    } catch (e) {
      /* if(e is ExceddedHoursSumException){
        get_roadMap2(total_subjects, cloned_semesters, remainig_hours, remaining_semesters_number-1, hours_sum+new_semester.hours, current_semester_number+1);
        print_msg('catched ExceddedHoursSumException');
      } else if ( e is UnreachedGraduationHoursException ){

      } */
      print_msg('catched error'); 
      print_msg("hours sum : ${hours_sum}, remaining_hours: ${remainig_hours}");
    }
    return [...cloned_semesters];
   }

  List<Semester> fill_semesters(List<Subject> total_subjects, int current_semester_number, List<Semester> res_semesters, int remainig_hours, int hours_sum) {
    print('fill_semesters(total_subjects: ${total_subjects.length},current_semester_number: ${current_semester_number}, res_semesters: ${res_semesters}, remaining_horus: ${remainig_hours})');
    // if done filling 10 semesters
    if(current_semester_number > 10){
      return [...res_semesters];
    }

    // if done filling remainig_hours
    if(hours_sum == remainig_hours){
      print_msg('done filling remaing hours');
      return [...res_semesters];
    }
    
    if(hours_sum >= remainig_hours){
      print_msg('return null');
      throw ExceddedHoursSumException('remaining hours: ${remainig_hours}, hours sum: ${hours_sum}');
    }

    /* bool res = check_constraint();
      if(!res){
        return null;
      }
     */
      // FilterSubjects filter_subjects_class = new FilterSubjects(total_subjects);
      // List<Subject> filtered_subjects = filter_subjects_class.get_possible_subjects_v2();
  

      List<Subject> filtered_subjects = get_possible_subjects(total_subjects);


      total_subjects.forEach((single_subject){
        for (int i=0; i < filtered_subjects.length; i++){
          if(filtered_subjects[i].code == single_subject.code){
            single_subject.status = true;
            break;
          }
        }
      });
      Semester new_semester = fill_semester(total_subjects, current_semester_number, optimal_semester_hours);
      res_semesters.add(new_semester);
      hours_sum += new_semester.hours; 
      try {
        fill_semesters(total_subjects, current_semester_number+1, res_semesters, remainig_hours, hours_sum);
      } catch (e) {
        if(e is ExceddedHoursSumException){
          if(hours_sum > remainig_hours){
            throw ExceddedHoursSumException('remaining hours: ${remainig_hours}, hours sum: ${hours_sum}');
          }
          fill_semesters(total_subjects, current_semester_number+1, res_semesters, remainig_hours, hours_sum);
          print_msg('catched ExceddedHoursSumException');
        } 
        print_msg('catched error'); 
        print_msg("hours sum : ${hours_sum}, remaining_hours: ${remainig_hours}");
      }
      return [...res_semesters];
  }

List<Semester>? get_roadMap(int remaining_hours) {
  List<Semester> res_semesters = [];
  try {
    this.fill_semesters(this.subjects, 3, res_semesters, remaining_hours, 0);
  }catch (e){
    if(e is ExceddedHoursSumException){
      print_msg('catched ExceddedHoursSumException');
    }
  }
  return res_semesters;
}
}

void main() async{

  String file_path = 'data/GPE.txt';
  
  List<Subject> subjects = await loadSubjectsFromFile(file_path);
  print(subjects.length);
  //Department dep = new Department(total_hours: 30, code: "code", arabicName: "arabicName", englishName: "englishName", departmentType: "departmentType", collegeType: "collegeType");
  //Lay7a lay7a = new Lay7a(year: 2019, subjects: subjects, collegeType: "collegeType", department: dep);

  int max_semester_hours = 21;
  int optimal_semester_hours = 18;
  int min_semester_hours = 12;

  RoadMap roadMap = new RoadMap(subjects, max_semester_hours, optimal_semester_hours, min_semester_hours);
  //var roadmap = roadMap.get_roadMap(50);
  var roadmap = roadMap.get_roadMap2(subjects, [], 50, 5, 0, 6);
  if(roadmap != null){
      for (int i = 0 ;i < roadmap.length; i++){
      var semester_hours_sum = 0;
      for(var j =0; j< roadmap[i].subjects.length; j++){
        semester_hours_sum += roadmap[i].subjects[j].hours;
      }
      print('semester: ${roadmap[i].number}, subjects count: ${roadmap[i].subjects.length}, semster hours sum: ${semester_hours_sum}');
      print("---------------------------------");
    }
  }
}