import 'Subject.dart';
import 'Semesetr.dart';
import '../functions/filtering_stage.dart';

/// Simple roadmap generation algorithm using backtracking
/// Fills semesters with subjects until graduation requirements are met
class SimpleRoadMap {
  List<Subject> subjects;
  int current_year = DateTime.now().year;
  int? graduation_year;
  String? semester_hours_json;
  int max_semester_hours;
  int optimal_semester_hours;
  int min_semester_hours;
  int required_graduation_hours;
  
  SimpleRoadMap(
    this.subjects,
    this.max_semester_hours,
    this.optimal_semester_hours,
    this.min_semester_hours, 
    this.required_graduation_hours,
    {
    this.graduation_year,
    this.semester_hours_json,
  }) {
    // Calculate total required hours from all subjects
  }

  /// Main method to generate roadmap
  /// Returns list of semesters or null if impossible to graduate by target year
  List<Semester>? generateRoadMap(int targetGraduationYear) {
    graduation_year = targetGraduationYear;
    
    // Calculate maximum available semesters
    int yearsAvailable = targetGraduationYear - current_year;
    int maxSemesters = yearsAvailable * 2; // 2 semesters per year
    
    if (maxSemesters <= 0) {
      print("Error: Target graduation year must be in the future");
      return null;
    }
    
    // Check if graduation is theoretically possible
    int maxPossibleHours = maxSemesters * max_semester_hours;
    if (required_graduation_hours > maxPossibleHours) {
      print("Error: Cannot graduate by $targetGraduationYear. Need $required_graduation_hours hours, but only $maxPossibleHours hours available.");
      return null;
    }
    
    // Create working copy of subjects
    List<Subject> workingSubjects = subjects.map((s) => s.clone()).toList();
    
    // Start backtracking algorithm
    List<Semester> roadmap = [];
    bool success = _backtrackFillSemesters(workingSubjects, roadmap, maxSemesters, 1);
    
    if (success) {
      print("✅ Roadmap generated successfully!");
      _printRoadmapSummary(roadmap);
      return roadmap;
    } else {
      print("❌ Could not generate valid roadmap for graduation year $targetGraduationYear");
      return null;
    }
  }

  /// Recursive backtracking method to fill semesters
  bool _backtrackFillSemesters(
    List<Subject> remainingSubjects,
    List<Semester> currentRoadmap,
    int maxSemesters,
    int currentSemesterNumber,
  ) {
    // Base case: Check if we've completed all subjects
    List<Subject> incompleteSubjects = remainingSubjects.where((s) => !s.status).toList();
    
    if (incompleteSubjects.isEmpty) {
      // All subjects completed - success!
      return true;
    }
    
    // Check if we've exceeded maximum semesters
    if (currentSemesterNumber > maxSemesters) {
      // Reached semester limit but still have incomplete subjects
      return false;
    }
    
    // Special case: If this is the last semester, we must complete all remaining subjects
    if (currentSemesterNumber == maxSemesters) {
      int remainingHours = incompleteSubjects.fold(0, (sum, s) => sum + s.hours);
      if (remainingHours > max_semester_hours) {
        return false; // Cannot fit all remaining subjects in last semester
      }
    }
    
    // Get subjects that can be taken this semester
    List<Subject> possibleSubjects = get_possible_subjects(remainingSubjects);
    
    if (possibleSubjects.isEmpty) {
      // No subjects available - this might be okay if all subjects are completed
      if (incompleteSubjects.isEmpty) {
        return true;
      } else {
        return false; // Still have subjects but none are available
      }
    }
    
    // Try different combinations of subjects for this semester
    return _trySubjectCombinations(
      possibleSubjects,
      remainingSubjects,
      currentRoadmap,
      maxSemesters,
      currentSemesterNumber,
    );
  }

  /// Try different combinations of subjects for current semester
  bool _trySubjectCombinations(
    List<Subject> possibleSubjects,
    List<Subject> remainingSubjects,
    List<Semester> currentRoadmap,
    int maxSemesters,
    int currentSemesterNumber,
  ) {
    // Sort subjects by priority (prerequisites first, then by semester number)
    possibleSubjects.sort((a, b) {
      if (a.semester_number != b.semester_number) {
        return a.semester_number.compareTo(b.semester_number);
      }
      return a.hours.compareTo(b.hours); // Prefer smaller subjects first
    });
    
    // Try different semester hour targets (optimal first, then min, then max)
    List<int> hourTargets = [optimal_semester_hours, min_semester_hours, max_semester_hours];
    
    for (int targetHours in hourTargets) {
      List<Subject> semesterSubjects = _selectSubjectsForSemester(possibleSubjects, targetHours);
      
      if (semesterSubjects.isNotEmpty) {
        // Create semester with selected subjects
        Semester semester = Semester(number: currentSemesterNumber);
        for (Subject subject in semesterSubjects) {
          semester.addSubject(subject);
        }
        
        // Mark subjects as completed in working copy
        List<Subject> newRemainingSubjects = remainingSubjects.map((s) => s.clone()).toList();
        for (Subject subject in semesterSubjects) {
          Subject? workingSubject = newRemainingSubjects.firstWhere(
            (s) => s.code == subject.code,
            orElse: () => Subject(
              semester_number: 0,
              status: false,
              code: '',
              englishName: '',
              arabicName: '',
              dependencies: [],
              prerequisites: [],
              type: '',
              departmentCode: '',
              hours: 0,
            ),
          );
          if (workingSubject.code.isNotEmpty) {
            workingSubject.status = true;
          }
        }
        
        // Add semester to roadmap
        currentRoadmap.add(semester);
        
        // Recursively try to fill next semester
        bool success = _backtrackFillSemesters(
          newRemainingSubjects,
          currentRoadmap,
          maxSemesters,
          currentSemesterNumber + 1,
        );
        
        if (success) {
          return true; // Found valid solution
        }
        
        // Backtrack: remove semester and try next combination
        currentRoadmap.removeLast();
      }
    }
    
    return false; // No valid combination found
  }

  /// Select subjects for a semester within hour constraints
  List<Subject> _selectSubjectsForSemester(List<Subject> availableSubjects, int targetHours) {
    List<Subject> selected = [];
    int currentHours = 0;
    
    for (Subject subject in availableSubjects) {
      if (currentHours + subject.hours <= targetHours) {
        selected.add(subject);
        currentHours += subject.hours;
        
        // If we've reached optimal hours, stop adding more subjects
        if (currentHours >= optimal_semester_hours) {
          break;
        }
      }
    }
    
    // Ensure we meet minimum hours if possible
    if (currentHours < min_semester_hours && currentHours > 0) {
      // Try to add one more subject if it doesn't exceed max hours
      for (Subject subject in availableSubjects) {
        if (!selected.contains(subject) && currentHours + subject.hours <= max_semester_hours) {
          selected.add(subject);
          currentHours += subject.hours;
          break;
        }
      }
    }
    
    return selected;
  }

  /// Print summary of generated roadmap
  void _printRoadmapSummary(List<Semester> roadmap) {
    print("\n📋 Roadmap Summary:");
    print("=" * 50);
    
    int totalHours = 0;
    for (int i = 0; i < roadmap.length; i++) {
      Semester semester = roadmap[i];
      totalHours += semester.hours;
      
      int year = current_year + (semester.number - 1) ~/ 2;
      String semesterType = (semester.number % 2 == 1) ? "Fall" : "Spring";
      
      print("Semester ${semester.number} ($semesterType $year): ${semester.hours} hours");
      for (Subject subject in semester.subjects) {
        print("  - ${subject.code}: ${subject.englishName} (${subject.hours}h)");
      }
      print("");
    }
    
    print("Total Hours: $totalHours / $required_graduation_hours");
    print("Graduation: ${graduation_year}");
    print("=" * 50);
  }

  /// Validate if a roadmap meets graduation requirements
  bool validateRoadmap(List<Semester> roadmap) {
    int totalHours = roadmap.fold(0, (sum, semester) => sum + semester.hours);
    
    // Check total hours
    if (totalHours < required_graduation_hours) {
      print("❌ Insufficient hours: $totalHours / $required_graduation_hours");
      return false;
    }
    
    // Check semester hour limits
    for (Semester semester in roadmap) {
      if (semester.hours > max_semester_hours) {
        print("❌ Semester ${semester.number} exceeds max hours: ${semester.hours} / $max_semester_hours");
        return false;
      }
      if (semester.hours > 0 && semester.hours < min_semester_hours) {
        print("⚠️ Semester ${semester.number} below min hours: ${semester.hours} / $min_semester_hours");
      }
    }
    
    // Check prerequisites
    Set<String> completedSubjects = {};
    for (Semester semester in roadmap) {
      for (Subject subject in semester.subjects) {
        // Check if prerequisites are met
        for (String prereq in subject.prerequisites) {
          if (!completedSubjects.contains(prereq)) {
            print("❌ Prerequisite not met: ${subject.code} requires $prereq");
            return false;
          }
        }
        completedSubjects.add(subject.code);
      }
    }
    
    print("✅ Roadmap validation passed!");
    return true;
  }
}
