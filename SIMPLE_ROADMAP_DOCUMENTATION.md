# 🚀 SimpleRoadMap Algorithm Documentation

## Overview

The `SimpleRoadMap` class implements a clean, efficient backtracking algorithm for generating academic roadmaps. It solves the problem of scheduling subjects across semesters to meet graduation requirements while respecting prerequisites and hour constraints.

## ✨ Key Features

- **🎯 Backtracking Algorithm**: Uses recursive backtracking to find valid semester combinations
- **📋 Prerequisite Validation**: Ensures all prerequisites are met before scheduling subjects
- **⏰ Hour Constraints**: Respects minimum, optimal, and maximum hours per semester
- **🎓 Graduation Timeline**: Calculates if graduation is possible by target year
- **✅ Roadmap Validation**: Comprehensive validation of generated roadmaps
- **🔄 Flexible Scheduling**: Tries multiple hour targets (optimal, min, max) for best fit

## 🏗️ Algorithm Design

### Core Approach
1. **Calculate Feasibility**: Check if graduation is theoretically possible
2. **Backtracking Search**: Recursively fill semesters with valid subjects
3. **Subject Selection**: Choose subjects based on prerequisites and hour constraints
4. **Validation**: Ensure all requirements are met

### Backtracking Strategy
```
For each semester:
  1. Get subjects that can be taken (prerequisites met)
  2. Try different hour targets (optimal → min → max)
  3. Select subjects that fit the hour target
  4. Recursively solve remaining semesters
  5. If solution found, return success
  6. If no solution, backtrack and try next combination
```

## 📊 Class Structure

### Constructor Parameters
```dart
SimpleRoadMap(
  List<Subject> subjects,           // All subjects in the program
  int max_semester_hours,           // Maximum hours per semester
  int optimal_semester_hours,       // Target hours per semester
  int min_semester_hours,           // Minimum hours per semester
  {
    int? graduation_year,           // Optional target graduation year
    String? semester_hours_json,    // Optional JSON configuration
  }
)
```

### Key Properties
- `required_graduation_hours`: Total hours needed (calculated from subjects)
- `current_year`: Current year (defaults to DateTime.now().year)
- `graduation_year`: Target graduation year

## 🎯 Main Methods

### `generateRoadMap(int targetGraduationYear)`
**Purpose**: Generate a complete roadmap for graduation by target year

**Returns**: `List<Semester>?` - List of semesters or null if impossible

**Algorithm**:
1. Calculate maximum available semesters
2. Check theoretical feasibility
3. Create working copy of subjects
4. Start backtracking algorithm
5. Return roadmap or null

### `validateRoadmap(List<Semester> roadmap)`
**Purpose**: Validate that a roadmap meets all requirements

**Checks**:
- ✅ Total hours meet graduation requirements
- ✅ Semester hours within limits
- ✅ Prerequisites are satisfied
- ✅ Subject scheduling order is correct

## 🔧 Usage Examples

### Basic Usage
```dart
// Create subjects list
List<Subject> subjects = [...];

// Create roadmap generator
SimpleRoadMap roadMap = SimpleRoadMap(
  subjects,
  18, // max hours
  15, // optimal hours
  12, // min hours
);

// Generate roadmap
int targetYear = DateTime.now().year + 3;
List<Semester>? roadmap = roadMap.generateRoadMap(targetYear);

if (roadmap != null) {
  print('✅ Roadmap generated successfully!');
  // Use the roadmap
} else {
  print('❌ Cannot graduate by $targetYear');
}
```

### Validation
```dart
// Validate generated roadmap
bool isValid = roadMap.validateRoadmap(roadmap);
if (isValid) {
  print('✅ Roadmap is valid');
} else {
  print('❌ Roadmap has issues');
}
```

## 📈 Algorithm Complexity

### Time Complexity
- **Best Case**: O(n) - when subjects can be scheduled linearly
- **Average Case**: O(n × s) - where n = subjects, s = semesters
- **Worst Case**: O(2^n) - when extensive backtracking is needed

### Space Complexity
- **Memory**: O(n + s) - for subject copies and semester storage
- **Stack**: O(s) - for recursive calls (max semester depth)

## 🎯 Algorithm Advantages

### ✅ Strengths
1. **Correctness**: Guarantees valid solutions when they exist
2. **Completeness**: Finds solutions if any exist
3. **Flexibility**: Handles complex prerequisite chains
4. **Efficiency**: Optimizes for preferred hour targets
5. **Validation**: Comprehensive error checking
6. **Readability**: Clean, understandable code

### ⚠️ Considerations
1. **Performance**: Can be slow for very large programs (100+ subjects)
2. **Memory**: Creates subject copies for backtracking
3. **Optimization**: May not find the absolute optimal solution

## 🧪 Testing

### Test Coverage
- ✅ **10 comprehensive test cases**
- ✅ **Realistic graduation scenarios**
- ✅ **Hour constraint validation**
- ✅ **Prerequisite chain testing**
- ✅ **Edge case handling**
- ✅ **Error condition testing**

### Test Scenarios
1. **Normal Cases**: Realistic graduation timelines
2. **Edge Cases**: Impossible timelines, empty subjects
3. **Validation**: Prerequisite violations, hour limits
4. **Performance**: Large subject lists, complex chains

## 🚀 Running Tests

```bash
# Run SimpleRoadMap tests only
dart run_tests.dart simple

# Run all tests including SimpleRoadMap
dart run_tests.dart all

# Run the demo
dart demo_simple_roadmap.dart
```

## 📋 Sample Output

```
✅ Roadmap generated successfully!

📋 Roadmap Summary:
==================================================
Semester 1 (Fall 2025): 14 hours
  - CS101: Programming Fundamentals (3h)
  - ENG101: English I (3h)
  - MATH101: Calculus I (4h)
  - PHYS101: Physics I (4h)

Semester 2 (Spring 2025): 13 hours
  - CS102: Data Structures (3h)
  - ENG102: English II (3h)
  - MATH102: Calculus II (4h)
  - CS201: Algorithms (3h)

Total Hours: 45 / 45
Graduation: 2028
==================================================
```

## 🔮 Future Enhancements

### Potential Improvements
1. **Optimization**: Add heuristics for better performance
2. **Summer Courses**: Support for summer semester scheduling
3. **Course Conflicts**: Handle time slot conflicts
4. **GPA Integration**: Consider GPA-based hour limits
5. **Multiple Majors**: Support for double majors/minors
6. **Elective Selection**: Smart elective course selection

### Integration Options
1. **Web Interface**: REST API for web applications
2. **Database Integration**: Direct database connectivity
3. **Export Features**: PDF/Excel roadmap export
4. **Notification System**: Deadline and registration reminders

## 🎉 Conclusion

The SimpleRoadMap algorithm provides a robust, efficient solution for academic roadmap generation. It successfully handles complex prerequisite chains, respects hour constraints, and provides comprehensive validation - making it suitable for real-world academic planning systems.

The algorithm's backtracking approach ensures correctness while maintaining reasonable performance for typical academic programs. With comprehensive testing and clear documentation, it's ready for integration into larger academic management systems.
